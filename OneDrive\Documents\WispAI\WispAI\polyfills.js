/**
 * Polyfills for Node.js modules that don't work in React Native
 * This file provides empty implementations or React Native alternatives
 */

// Polyfill for 'stream' module - provide simple implementations
const streamPolyfill = {
  Duplex: class Duplex {
    constructor() {
      console.warn('Stream Duplex is not supported in React Native');
    }
  },
  Readable: class Readable {
    constructor() {
      console.warn('Stream Readable is not supported in React Native');
    }
  },
  Writable: class Writable {
    constructor() {
      console.warn('Stream Writable is not supported in React Native');
    }
  },
  Transform: class Transform {
    constructor() {
      console.warn('Stream Transform is not supported in React Native');
    }
  },
  PassThrough: class PassThrough {
    constructor() {
      console.warn('Stream PassThrough is not supported in React Native');
    }
  },
};

global.stream = streamPolyfill;

// Polyfill for 'https' module - provide simple implementations
const httpsPolyfill = {
  request: function() {
    console.warn('HTTPS requests are not supported in React Native. Use fetch() instead.');
    return {
      on: function() {},
      end: function() {},
      write: function() {},
      abort: function() {},
    };
  },
  get: function() {
    console.warn('HTTPS GET requests are not supported in React Native. Use fetch() instead.');
    return {
      on: function() {},
      end: function() {},
    };
  },
  Agent: class HttpsAgent {
    constructor() {
      console.warn('HTTPS Agent is not supported in React Native');
    }
  },
};

global.https = httpsPolyfill;

// Polyfill for 'http' module - provide simple implementations
const httpPolyfill = {
  request: function() {
    console.warn('HTTP requests are not supported in React Native. Use fetch() instead.');
    return {
      on: function() {},
      end: function() {},
      write: function() {},
      abort: function() {},
    };
  },
  get: function() {
    console.warn('HTTP GET requests are not supported in React Native. Use fetch() instead.');
    return {
      on: function() {},
      end: function() {},
    };
  },
  Agent: class HttpAgent {
    constructor() {
      console.warn('HTTP Agent is not supported in React Native');
    }
  },
};

global.http = httpPolyfill;

// Polyfill for 'net' module - provide simple implementations
const netPolyfill = {
  connect: function() {
    console.warn('Net connections are not supported in React Native');
    return {
      on: function() {},
      write: function() {},
      end: function() {},
      destroy: function() {},
    };
  },
  createConnection: function() {
    console.warn('Net connections are not supported in React Native');
    return {
      on: function() {},
      write: function() {},
      end: function() {},
      destroy: function() {},
    };
  },
  Socket: class Socket {
    constructor() {
      console.warn('Net Socket is not supported in React Native');
    }
    on() { return this; }
    write() { return this; }
    end() { return this; }
    destroy() { return this; }
  },
};

global.net = netPolyfill;

// Polyfill for 'tls' module - provide simple implementations
const tlsPolyfill = {
  connect: function() {
    console.warn('TLS connections are not supported in React Native');
    return {
      on: function() {},
      write: function() {},
      end: function() {},
      destroy: function() {},
    };
  },
  TLSSocket: class TLSSocket {
    constructor() {
      console.warn('TLS Socket is not supported in React Native');
    }
    on() { return this; }
    write() { return this; }
    end() { return this; }
    destroy() { return this; }
  },
};

global.tls = tlsPolyfill;

// Polyfill for 'url' module - provide simple implementations
const urlPolyfill = {
  parse: function(urlString) {
    console.warn('URL parsing in React Native should use the native URL constructor');
    try {
      const url = new URL(urlString);
      return {
        protocol: url.protocol,
        hostname: url.hostname,
        port: url.port,
        pathname: url.pathname,
        search: url.search,
        hash: url.hash,
        host: url.host,
        href: url.href,
      };
    } catch (e) {
      return {};
    }
  },
  format: function(urlObject) {
    console.warn('URL formatting in React Native should use the native URL constructor');
    return urlObject.href || '';
  },
  resolve: function(from, to) {
    console.warn('URL resolving in React Native should use the native URL constructor');
    try {
      return new URL(to, from).href;
    } catch (e) {
      return to;
    }
  },
  URL: global.URL || class URL {
    constructor(url, base) {
      console.warn('URL constructor polyfill - use native URL if available');
      this.href = url;
    }
  },
};

global.url = urlPolyfill;

// Polyfill for 'events' module - provide EventEmitter implementation
const eventsPolyfill = {
  EventEmitter: class EventEmitter {
    constructor() {
      this._events = {};
      this._maxListeners = 10;
    }

    on(event, listener) {
      if (!this._events[event]) {
        this._events[event] = [];
      }
      this._events[event].push(listener);
      return this;
    }

    addListener(event, listener) {
      return this.on(event, listener);
    }

    once(event, listener) {
      const onceWrapper = (...args) => {
        this.removeListener(event, onceWrapper);
        listener.apply(this, args);
      };
      return this.on(event, onceWrapper);
    }

    removeListener(event, listener) {
      if (!this._events[event]) return this;
      const index = this._events[event].indexOf(listener);
      if (index !== -1) {
        this._events[event].splice(index, 1);
      }
      return this;
    }

    removeAllListeners(event) {
      if (event) {
        delete this._events[event];
      } else {
        this._events = {};
      }
      return this;
    }

    emit(event, ...args) {
      if (!this._events[event]) return false;
      this._events[event].forEach(listener => {
        try {
          listener.apply(this, args);
        } catch (error) {
          console.error('Error in event listener:', error);
        }
      });
      return true;
    }

    listeners(event) {
      return this._events[event] ? [...this._events[event]] : [];
    }

    listenerCount(event) {
      return this._events[event] ? this._events[event].length : 0;
    }

    setMaxListeners(n) {
      this._maxListeners = n;
      return this;
    }

    getMaxListeners() {
      return this._maxListeners;
    }
  },
};

global.events = eventsPolyfill;

// Polyfill for 'crypto' module
const cryptoPolyfill = {
  randomBytes: function(size) {
    const array = new Uint8Array(size);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      // Fallback for environments without crypto.getRandomValues
      for (let i = 0; i < size; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return array;
  },
  createHash: function(algorithm) {
    console.warn(`Crypto hash ${algorithm} is not supported in React Native`);
    return {
      update: function() { return this; },
      digest: function() { return ''; }
    };
  },
  createHmac: function(algorithm, key) {
    console.warn(`Crypto HMAC ${algorithm} is not supported in React Native`);
    return {
      update: function() { return this; },
      digest: function() { return ''; }
    };
  }
};

global.crypto = cryptoPolyfill;

// Polyfill for Node.js modules that should not be used in React Native
const noopModule = () => {
  console.warn('This Node.js module is not available in React Native');
  return {};
};

const noopClass = class NoopClass {
  constructor() {
    console.warn('This Node.js class is not available in React Native');
  }
};

// Create no-op implementations for Node.js modules that we don't polyfill
global.fs = noopModule;
global.path = noopModule;
global.os = noopModule;
global.util = noopModule;
global.buffer = { Buffer: noopClass };
global.querystring = noopModule;

// Polyfill for zlib (compression library) - not needed in React Native
const zlibPolyfill = {
  Z_DEFAULT_WINDOWBITS: 15,
  Z_SYNC_FLUSH: 2,
  createInflateRaw: (options) => {
    console.warn('zlib compression is not supported in React Native');
    return {
      write: () => {},
      flush: (callback) => { if (typeof callback === 'function') callback(); },
      close: () => {},
      reset: () => {},
      on: () => {},
      removeListener: () => {},
      _readableState: { endEmitted: false }
    };
  },
  createDeflateRaw: (options) => {
    console.warn('zlib compression is not supported in React Native');
    return {
      write: () => {},
      flush: (type, callback) => {
        if (typeof type === 'function') type();
        if (typeof callback === 'function') callback();
      },
      close: () => {},
      reset: () => {},
      on: () => {}
    };
  }
};

// Comprehensive polyfill for 'ws' package
const wsPolyfill = {
  // Main WebSocket class - use React Native's native WebSocket
  WebSocket: global.WebSocket || class WebSocket {
    constructor(url, protocols) {
      console.warn('WebSocket from ws package is not available in React Native. Use the native WebSocket API instead.');
      // Return a minimal WebSocket-like object
      this.readyState = 3; // CLOSED
      this.url = url;
      this.protocol = '';
      this.extensions = '';
      this.bufferedAmount = 0;
      this.binaryType = 'blob';

      // Event handlers
      this.onopen = null;
      this.onclose = null;
      this.onmessage = null;
      this.onerror = null;

      // Methods
      this.send = function() {
        console.warn('WebSocket send not available in polyfill');
      };
      this.close = function() {
        console.warn('WebSocket close not available in polyfill');
      };
      this.addEventListener = function() {};
      this.removeEventListener = function() {};
      this.dispatchEvent = function() { return false; };
    }
  },

  // WebSocket Server (not available in React Native)
  WebSocketServer: class WebSocketServer {
    constructor() {
      console.warn('WebSocketServer is not available in React Native');
      return {};
    }
  },

  // WebSocket Receiver (not available in React Native)
  Receiver: class Receiver {
    constructor() {
      console.warn('WebSocket Receiver is not available in React Native');
      return {};
    }
  },

  // WebSocket Sender (not available in React Native)
  Sender: class Sender {
    constructor() {
      console.warn('WebSocket Sender is not available in React Native');
      return {};
    }
  },

  // Stream creation function
  createWebSocketStream: () => {
    console.warn('WebSocket streams are not supported in React Native');
    return {};
  },

  // Constants
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
};

// Export the polyfill as both default and named exports
module.exports = wsPolyfill;
module.exports.default = wsPolyfill;
module.exports.WebSocket = wsPolyfill.WebSocket;
module.exports.WebSocketServer = wsPolyfill.WebSocketServer;
module.exports.Receiver = wsPolyfill.Receiver;
module.exports.Sender = wsPolyfill.Sender;
module.exports.createWebSocketStream = wsPolyfill.createWebSocketStream;

// Also export crypto polyfill
module.exports.crypto = cryptoPolyfill;
module.exports.randomBytes = cryptoPolyfill.randomBytes;
module.exports.createHash = cryptoPolyfill.createHash;
module.exports.createHmac = cryptoPolyfill.createHmac;

// Also export stream polyfill
module.exports.stream = streamPolyfill;
module.exports.Duplex = streamPolyfill.Duplex;
module.exports.Readable = streamPolyfill.Readable;
module.exports.Writable = streamPolyfill.Writable;
module.exports.Transform = streamPolyfill.Transform;
module.exports.PassThrough = streamPolyfill.PassThrough;

// Export Node.js module polyfills
module.exports.https = httpsPolyfill;
module.exports.http = httpPolyfill;
module.exports.net = netPolyfill;
module.exports.tls = tlsPolyfill;
module.exports.url = urlPolyfill;
module.exports.events = eventsPolyfill;
module.exports.EventEmitter = eventsPolyfill.EventEmitter;
module.exports.zlib = zlibPolyfill;
module.exports.crypto = cryptoPolyfill;
module.exports.stream = streamPolyfill;

// For ws/lib/websocket.js - export the WebSocket class as default
module.exports.default = wsPolyfill.WebSocket;

// For ws/lib/permessage-deflate.js - export zlib constants and functions
module.exports.Z_DEFAULT_WINDOWBITS = zlibPolyfill.Z_DEFAULT_WINDOWBITS;
module.exports.Z_SYNC_FLUSH = zlibPolyfill.Z_SYNC_FLUSH;
module.exports.createInflateRaw = zlibPolyfill.createInflateRaw;
module.exports.createDeflateRaw = zlibPolyfill.createDeflateRaw;

// Set global references
global.ws = wsPolyfill;

console.log('React Native polyfills loaded successfully');
