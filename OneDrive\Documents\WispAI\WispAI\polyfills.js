/**
 * Polyfills for Node.js modules that don't work in React Native
 * This file provides empty implementations or React Native alternatives
 */

// Import React Native polyfill globals
import 'react-native-polyfill-globals/auto';

// Polyfill for 'stream' module
try {
  if (typeof global.stream === 'undefined') {
    global.stream = require('react-native-stream');
  }
} catch (e) {
  // Fallback if react-native-stream is not available
  global.stream = {
    Duplex: class Duplex {},
    Readable: class Readable {},
    Writable: class Writable {},
    Transform: class Transform {},
    PassThrough: class PassThrough {},
  };
}

// Polyfill for 'crypto' module
try {
  if (typeof global.crypto === 'undefined') {
    global.crypto = require('react-native-crypto');
  }
} catch (e) {
  // Fallback crypto implementation
  global.crypto = {
    randomBytes: () => new Uint8Array(0),
    createHash: () => ({ update: () => {}, digest: () => '' }),
  };
}

// Polyfill for Node.js modules that should not be used in React Native
const noopModule = () => {
  console.warn('This Node.js module is not available in React Native');
  return {};
};

const noopClass = class NoopClass {
  constructor() {
    console.warn('This Node.js class is not available in React Native');
  }
};

// Create no-op implementations for Node.js modules
global.http = noopModule;
global.https = noopModule;
global.net = noopModule;
global.tls = noopModule;
global.fs = noopModule;
global.path = noopModule;
global.os = noopModule;
global.util = noopModule;
global.events = { EventEmitter: noopClass };
global.buffer = { Buffer: noopClass };
global.url = noopModule;
global.querystring = noopModule;

// Comprehensive polyfill for 'ws' package
const wsPolyfill = {
  // Main WebSocket class
  WebSocket: class WebSocket {
    constructor() {
      console.warn('WebSocket from ws package is not available in React Native. Use the native WebSocket API instead.');
      return {};
    }
  },

  // WebSocket Server (not available in React Native)
  WebSocketServer: class WebSocketServer {
    constructor() {
      console.warn('WebSocketServer is not available in React Native');
      return {};
    }
  },

  // Stream creation function
  createWebSocketStream: () => {
    console.warn('WebSocket streams are not supported in React Native');
    return {};
  },

  // Constants
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
};

// Export the polyfill as both default and named exports
module.exports = wsPolyfill;
module.exports.default = wsPolyfill;
module.exports.WebSocket = wsPolyfill.WebSocket;
module.exports.WebSocketServer = wsPolyfill.WebSocketServer;
module.exports.createWebSocketStream = wsPolyfill.createWebSocketStream;

// Set global references
global.ws = wsPolyfill;

console.log('React Native polyfills loaded successfully');
