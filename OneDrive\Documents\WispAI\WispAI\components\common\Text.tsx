import React from 'react';
import { Text as RNText, TextStyle, TextProps as RNTextProps } from 'react-native';
import { useTheme } from '../../contexts/ThemeContext';
import { Typography } from '../../constants/Fonts';

interface TextProps extends RNTextProps {
  variant?: keyof typeof Typography;
  color?: string;
  align?: 'auto' | 'left' | 'right' | 'center' | 'justify';
  transform?: 'none' | 'capitalize' | 'uppercase' | 'lowercase';
  children: React.ReactNode;
}

/**
 * Enhanced Text component that uses the typography system
 */
export default function Text({
  variant = 'body1',
  color,
  align,
  transform,
  style,
  children,
  ...props
}: TextProps) {
  const { theme } = useTheme();

  // Get the typography style for the variant
  const variantStyle = Typography[variant];

  // Combine all styles
  const combinedStyle: TextStyle = {
    ...variantStyle,
    fontFamily: 'Satoshi-Regular', // Explicitly set the font family
    color: color || theme.colors.textPrimary,
    textAlign: align,
    textTransform: transform,
  };

  // For debugging
  console.log('Text component using font:', combinedStyle.fontFamily);

  return (
    <RNText style={[combinedStyle, style]} {...props}>
      {children}
    </RNText>
  );
}
