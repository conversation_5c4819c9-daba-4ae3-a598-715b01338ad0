import React from 'react';
import {
  View,
  Text as RNText,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  StatusBar,
} from 'react-native';
import { useRouter } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import Text from '../../components/common/Text';

// Define the Satoshi Regular font family name
const satoshiRegular = 'Satoshi-Regular';

const { width, height } = Dimensions.get('window');

export default function SubscriptionCTAScreen() {
  const router = useRouter();

  // Handle subscription button press
  const handleSubscribe = () => {
    // Navigate to welcome screen for now
    // In a real app, this would go to a payment processor
    router.replace('/(auth)/welcome');
  };

  // Handle close button press
  const handleClose = () => {
    // Navigate to welcome screen
    router.replace('/(auth)/welcome');
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />

      {/* Beautiful background gradient */}
      <LinearGradient
        colors={[
          '#BF2F9A', // Bright magenta
          '#D03AB0', // Medium magenta
          '#C135A0', // Magenta
          '#B03090', // Slightly darker magenta
          '#902580', // Dark magenta
          '#801E70', // Deeper magenta
          '#701660', // Very deep magenta
          '#501050', // Almost black magenta
          '#380838', // Very dark magenta
          '#280528', // Nearly black magenta
          '#180318', // Extremely dark magenta
          '#100210', // Almost black
          '#000000'  // Pure black
        ]}
        locations={[0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.45, 0.5, 0.6, 0.7]}
        start={{ x: 0.2, y: 0 }}
        end={{ x: 0.8, y: 1 }}
        style={StyleSheet.absoluteFillObject}
      />



      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Header */}
        <Text style={styles.title}>Unlock Your Full Potential</Text>
        <Text style={styles.subtitle}>Get the most out of your WispAI experience</Text>

        {/* Premium card */}
        <View style={styles.premiumCard}>
          <View style={styles.badgeContainer}>
            <Text style={styles.badgeText}>PREMIUM</Text>
          </View>

          <Text style={styles.premiumTitle}>WispAI Premium</Text>
          <Text style={styles.premiumPrice}>$4.99<Text style={styles.priceUnit}>/week</Text></Text>

          {/* Features list */}
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <MaterialIcons name="check-circle" size={20} color="#00F2EA" style={styles.featureIcon} />
              <Text style={styles.featureText}>Unlimited conversation analyses</Text>
            </View>
            <View style={styles.featureItem}>
              <MaterialIcons name="check-circle" size={20} color="#00F2EA" style={styles.featureIcon} />
              <Text style={styles.featureText}>Advanced ghosting detection</Text>
            </View>
            <View style={styles.featureItem}>
              <MaterialIcons name="check-circle" size={20} color="#00F2EA" style={styles.featureIcon} />
              <Text style={styles.featureText}>Personalized reply suggestions</Text>
            </View>
            <View style={styles.featureItem}>
              <MaterialIcons name="check-circle" size={20} color="#00F2EA" style={styles.featureIcon} />
              <Text style={styles.featureText}>Priority support</Text>
            </View>
            <View style={styles.featureItem}>
              <MaterialIcons name="check-circle" size={20} color="#00F2EA" style={styles.featureIcon} />
              <Text style={styles.featureText}>No ads</Text>
            </View>
          </View>

          {/* Subscribe button */}
          <TouchableOpacity style={styles.subscribeButton} onPress={handleSubscribe}>
            <Text style={styles.subscribeButtonText}>Start 3-Day Free Trial</Text>
          </TouchableOpacity>
          <Text style={styles.termsText}>
            After trial, subscription auto-renews. Cancel anytime.
          </Text>
        </View>

        {/* Free option */}
        <View style={styles.freeOption}>
          <TouchableOpacity style={styles.freeButton} onPress={handleClose}>
            <Text style={styles.freeButtonText}>Continue with Free Version</Text>
          </TouchableOpacity>
          <Text style={styles.freeDescription}>
            Limited to 3 analyses per week. Basic features only.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  closeButton: {
    position: 'absolute',
    top: 40,
    left: 20,
    zIndex: 10,
    padding: 5,
  },
  scrollContent: {
    flexGrow: 1,
    paddingTop: 80,
    paddingBottom: 40,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 25, // Reduced font size from 28 to 25
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 10,
    fontFamily: satoshiRegular,
    lineHeight: 33, // Reduced line height from 36 to 33
  },
  subtitle: {
    fontSize: 16,
    color: '#aaa',
    textAlign: 'center',
    marginBottom: 30,
    fontFamily: satoshiRegular,
    lineHeight: 24, // Added line height to prevent clipping
  },
  premiumCard: {
    backgroundColor: '#111',
    borderRadius: 20,
    padding: 25,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333',
    marginBottom: 30,
  },
  badgeContainer: {
    backgroundColor: '#00F2EA',
    paddingHorizontal: 15,
    paddingVertical: 5,
    borderRadius: 15,
    marginBottom: 15,
  },
  badgeText: {
    color: '#000',
    fontWeight: 'bold',
    fontSize: 14,
    fontFamily: satoshiRegular,
    lineHeight: 20, // Added line height to prevent clipping
  },
  premiumTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
    fontFamily: satoshiRegular,
    lineHeight: 32, // Added line height to prevent clipping
  },
  premiumPrice: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
    fontFamily: satoshiRegular,
    lineHeight: 44, // Added line height to prevent clipping
  },
  priceUnit: {
    fontSize: 18,
    fontWeight: 'normal',
    color: '#aaa',
    fontFamily: satoshiRegular,
    lineHeight: 26, // Added line height to prevent clipping
  },
  discountText: {
    fontSize: 14,
    color: '#aaa',
    marginBottom: 25,
    fontFamily: satoshiRegular,
    lineHeight: 20, // Added line height to prevent clipping
  },
  featuresList: {
    width: '100%',
    marginBottom: 25,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  featureIcon: {
    marginRight: 10,
  },
  featureText: {
    fontSize: 16,
    color: '#fff',
    fontFamily: satoshiRegular,
    lineHeight: 24, // Added line height to prevent clipping
  },
  subscribeButton: {
    backgroundColor: '#00F2EA',
    paddingVertical: 15,
    paddingHorizontal: 25,
    borderRadius: 30,
    width: '100%',
    alignItems: 'center',
    marginBottom: 15,
  },
  subscribeButtonText: {
    color: '#000',
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: satoshiRegular,
    lineHeight: 26, // Added line height to prevent clipping
  },
  termsText: {
    fontSize: 12,
    color: '#aaa',
    textAlign: 'center',
    fontFamily: satoshiRegular,
    lineHeight: 18, // Added line height to prevent clipping
  },
  freeOption: {
    width: '100%',
    alignItems: 'center',
  },
  freeButton: {
    paddingVertical: 15,
    paddingHorizontal: 25,
    borderRadius: 30,
    width: '100%',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#333',
    marginBottom: 10,
  },
  freeButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: satoshiRegular,
    lineHeight: 24, // Added line height to prevent clipping
  },
  freeDescription: {
    fontSize: 12,
    color: '#aaa',
    textAlign: 'center',
    fontFamily: satoshiRegular,
    lineHeight: 18, // Added line height to prevent clipping
  },
});
