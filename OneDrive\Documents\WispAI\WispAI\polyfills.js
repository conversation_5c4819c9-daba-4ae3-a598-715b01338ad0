/**
 * Polyfills for Node.js modules that don't work in React Native
 * This file provides empty implementations or React Native alternatives
 */

// Polyfill for 'stream' module
if (typeof global.stream === 'undefined') {
  global.stream = require('react-native-stream');
}

// Polyfill for 'crypto' module
if (typeof global.crypto === 'undefined') {
  global.crypto = require('react-native-crypto');
}

// Polyfill for 'ws' module - provide a no-op implementation
if (typeof global.WebSocket === 'undefined') {
  global.WebSocket = class WebSocket {
    constructor() {
      console.warn('WebSocket is not available in React Native. Use the native WebSocket API instead.');
    }
  };
}

// Polyfill for Node.js modules that should not be used in React Native
const noopModule = () => {
  throw new Error('This Node.js module is not available in React Native');
};

// Create no-op implementations for Node.js modules
global.http = noopModule;
global.https = noopModule;
global.net = noopModule;
global.tls = noopModule;
global.fs = noopModule;
global.path = noopModule;
global.os = noopModule;

// Polyfill for 'ws' package specifically
global.ws = {
  WebSocket: global.WebSocket,
  createWebSocketStream: () => {
    throw new Error('WebSocket streams are not supported in React Native');
  }
};

console.log('React Native polyfills loaded');
