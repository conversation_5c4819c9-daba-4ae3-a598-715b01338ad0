{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "baseUrl": ".", "jsx": "react-jsx", "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "paths": {"@/*": ["*"], "@components/*": ["components/*"], "@constants/*": ["constants/*"], "@hooks/*": ["hooks/*"], "@services/*": ["services/*"], "@store/*": ["store/*"], "@types/*": ["types/*"], "@utils/*": ["utils/*"], "@assets/*": ["assets/*"]}}, "include": ["**/*.ts", "**/*.tsx"]}