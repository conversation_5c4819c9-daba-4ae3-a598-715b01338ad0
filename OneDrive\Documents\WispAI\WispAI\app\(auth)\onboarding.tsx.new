import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  // Remove standard Text import
  // Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Animated,
  StatusBar,
} from 'react-native';
import { useRouter, useNavigation } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Import LinearGradient
import { LinearGradient } from 'expo-linear-gradient';
// Import useTheme
import { useTheme } from '../../contexts/ThemeContext';
// Import custom Text component
import Text from '../../components/common/Text';
// Import gradient functions
import { GradientType } from '../../constants/Gradients';
import { getQuestionGradient } from '../../constants/OnboardingGradients';

const { width, height } = Dimensions.get('window');

// Quiz questions and options
const quizQuestions = [
  {
    question: 'How do you usually start a convo?',
    options: [
      { id: 'a', text: '"Hey 😊"', icon: '👋' },
      { id: 'b', text: 'Meme or TikTok link', icon: '🎬' },
      { id: 'c', text: 'Deep thought at 2am', icon: '🌙' },
      { id: 'd', text: 'I wait for them to text first', icon: '⏱️' },
    ],
  },
  {
    question: 'What\'s your texting vibe?',
    options: [
      { id: 'a', text: 'Dry but loyal', icon: '🐢' },
      { id: 'b', text: 'Overthink every message', icon: '🤯' },
      { id: 'c', text: 'Smooth & flirty', icon: '😏' },
      { id: 'd', text: 'Left on read... by me 😌', icon: '🧊' },
    ],
  },
  {
    question: 'What\'s your biggest texting ick?',
    options: [
      { id: 'a', text: 'One-word replies', icon: '😒' },
      { id: 'b', text: 'Typing... then nothing', icon: '💬' },
      { id: 'c', text: 'Random ghosting', icon: '👻' },
      { id: 'd', text: '"k" 😐', icon: '🙄' },
    ],
  },
  {
    question: 'What does ghosting feel like to you?',
    options: [
      { id: 'a', text: 'Confusing', icon: '❓' },
      { id: 'b', text: 'Normal', icon: '🤷' },
      { id: 'c', text: 'Kind of funny', icon: '😆' },
      { id: 'd', text: 'Lowkey heartbreaking', icon: '💔' },
    ],
  },
  {
    question: 'When someone takes hours to reply, you...',
    options: [
      { id: 'a', text: 'Wait it out', icon: '⏳' },
      { id: 'b', text: 'Double-text 😬', icon: '📱' },
      { id: 'c', text: 'Do the same back', icon: '🪞' },
      { id: 'd', text: 'Unfollow. Next.', icon: '👋' },
    ],
  },
  {
    question: 'What are you hoping WispAI will do for you?',
    options: [
      { id: 'a', text: 'Decode mixed signals', icon: '🔍' },
      { id: 'b', text: 'Help me respond better', icon: '💬' },
      { id: 'c', text: 'Tell me if I\'m being ghosted', icon: '👻' },
      { id: 'd', text: 'Just for fun 👀', icon: '🎮' },
    ],
  },
  {
    question: 'What\'s your texting survival instinct?',
    options: [
      { id: 'a', text: 'Ghost them first 😤', icon: '🏃' },
      { id: 'b', text: 'Pretend I\'m unbothered (I\'m not)', icon: '😎' },
      { id: 'c', text: 'Analyze every word like a crime scene 🕵️', icon: '🔎' },
      { id: 'd', text: 'Hope for the best and text again anyway 💌', icon: '✨' },
    ],
  },
];

// Personality types based on quiz results
const personalityTypes = [
  {
    id: 'ghost_magnet',
    title: 'Ghost Magnet 👻',
    description: 'You attract the emotionally unavailable like moths to a flame. You\'re sweet, responsive, and maybe a little too forgiving. We\'re here to protect your heart.',
    color: '#E57373', // Light red
  },
  {
    id: 'overthinker',
    title: 'Overthinker 3000 🤯',
    description: 'You reread messages 8 times and analyze every emoji. You don\'t miss a beat — and neither does WispAI. Let\'s overanalyze together.',
    color: '#64B5F6', // Light blue
  },
  {
    id: 'idgaf',
    title: 'The IDGAF Texter 🧊',
    description: 'You\'re detached, mysterious, and always in control. But deep down? You kinda want to care. WispAI can decode what they really meant.',
    color: '#9575CD', // Light purple
  },
  {
    id: 'romantic',
    title: 'Hopeful Romantic 💌',
    description: 'You\'re in it for the real thing. You believe in vibes, late-night convos, and subtle flirting. WispAI helps you tell the real from the fake.',
    color: '#F06292', // Light pink
  },
  {
    id: 'flirt',
    title: 'The Flirt Bot 😏',
    description: 'You\'re a natural. Texting is your love language — but even pros can miss a red flag. WispAI keeps your game sharp.',
    color: '#FF8A65', // Light orange
  },
  {
    id: 'dry_texter',
    title: 'Dry Texter, Loyal Heart 🐢',
    description: 'You might not text novels, but you mean every word. WispAI helps make your short replies still hit deep.',
    color: '#4DB6AC', // Light teal
  },
  {
    id: 'mirror',
    title: 'The Mirror 🪞',
    description: 'You match energy. Quick replies? You\'re quick too. Ignored? Bye. But sometimes it\'s hard to tell what they\'re really feeling — that\'s where we come in.',
    color: '#FFD54F', // Light amber
  },
  {
    id: 'casual',
    title: 'Casual Until It\'s Serious 😎',
    description: 'You keep it chill until things get real. But when they do, you want clarity. WispAI helps you catch the shift before it happens.',
    color: '#81C784', // Light green
  },
];

export default function OnboardingScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  // Add theme context
  const { theme } = useTheme();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [showResult, setShowResult] = useState(false);
  const [personalityResult, setPersonalityResult] = useState(null);
  const [showLoading, setShowLoading] = useState(false);
  // Add state for current gradient using question-specific gradients
  const [currentGradient, setCurrentGradient] = useState<GradientType>(getQuestionGradient(0));

  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const loadingProgress = useRef(new Animated.Value(0)).current;
  const resultFadeAnim = useRef(new Animated.Value(0)).current;

  // Set empty header title on mount
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
      title: '',
    });
  }, [navigation]);

  // Handle option selection
  const handleSelectOption = (optionId) => {
    // Save the answer
    const newAnswers = { ...answers, [currentQuestionIndex]: optionId };
    setAnswers(newAnswers);

    // Animate transition
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -width,
        duration: 0,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Move to next question or show result
      if (currentQuestionIndex < quizQuestions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        // Set the specific gradient for the next question
        setCurrentGradient(getQuestionGradient(currentQuestionIndex + 1));
        // Reset animations
        fadeAnim.setValue(0);
        slideAnim.setValue(width);
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        // Show loading screen before results
        setShowLoading(true);

        // Animate loading progress
        Animated.timing(loadingProgress, {
          toValue: 1,
          duration: 3000, // 3 seconds loading animation
          useNativeDriver: false,
        }).start(() => {
          // Determine personality type based on answers
          const personalityType = determinePersonalityType(newAnswers);
          setPersonalityResult(personalityType);

          // Save personality type to AsyncStorage
          AsyncStorage.setItem('personalityType', personalityType.id);

          // Show result screen
          setShowResult(true);

          // Animate result screen
          Animated.timing(resultFadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }).start();
        });
      }

      // Reset animations for next question
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  // Determine personality type based on answers
  const determinePersonalityType = (userAnswers) => {
    // Simple algorithm to determine personality type
    // Count occurrences of each option
    const counts = { a: 0, b: 0, c: 0, d: 0 };

    Object.values(userAnswers).forEach(answer => {
      counts[answer]++;
    });

    // Find the most common answer
    let maxCount = 0;
    let dominantType = 'a';

    Object.entries(counts).forEach(([type, count]) => {
      if (count > maxCount) {
        maxCount = count;
        dominantType = type;
      }
    });

    // Map dominant answer to personality type
    let personalityIndex = 0;

    switch (dominantType) {
      case 'a':
        personalityIndex = counts.d > counts.b ? 0 : 5; // Ghost Magnet or Dry Texter
        break;
      case 'b':
        personalityIndex = counts.c > counts.a ? 1 : 6; // Overthinker or Mirror
        break;
      case 'c':
        personalityIndex = counts.b > counts.d ? 3 : 4; // Hopeful Romantic or Flirt Bot
        break;
      case 'd':
        personalityIndex = counts.a > counts.c ? 2 : 7; // IDGAF or Casual
        break;
      default:
        personalityIndex = 1; // Default to Overthinker
    }

    return personalityTypes[personalityIndex];
  };

  // Handle continue button on result screen
  const handleContinue = () => {
    router.replace('/(auth)/subscription-cta');
  };

  // Current question
  const currentQuestion = quizQuestions[currentQuestionIndex];

  // Loading bar width interpolation
  const loadingWidth = loadingProgress.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={styles.container}>
      {/* Add the gradient background */}
      <LinearGradient
        colors={currentGradient.colors}
        style={styles.background}
        start={currentGradient.start}
        end={currentGradient.end}
      />
      <StatusBar barStyle="light-content" />

      {!showLoading && !showResult && (
        <Animated.View
          style={[
            styles.questionContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateX: slideAnim }]
            }
          ]}
        >
          {/* Progress indicator */}
          <View style={styles.progressContainer}>
            {quizQuestions.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.progressDot,
                  index === currentQuestionIndex ? styles.progressDotActive : null
                ]}
              />
            ))}
          </View>

          {/* Question */}
          <Text style={styles.questionNumber}>Question {currentQuestionIndex + 1}/{quizQuestions.length}</Text>
          <Text style={styles.questionText}>{currentQuestion.question}</Text>

          {/* Options - Make sure this is scrollable and has enough height */}
          <ScrollView 
            style={styles.optionsContainer}
            contentContainerStyle={styles.optionsContentContainer}
            showsVerticalScrollIndicator={false}
          >
            {currentQuestion.options.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={styles.optionCard}
                onPress={() => handleSelectOption(option.id)}
                activeOpacity={0.8}
              >
                <Text style={styles.optionIcon}>{option.icon}</Text>
                <Text style={styles.optionText}>{option.text}</Text>
              </TouchableOpacity>
            ))}
            {/* Add extra padding at the bottom to ensure all options are visible */}
            <View style={styles.optionsPadding} />
          </ScrollView>
        </Animated.View>
      )}

      {showLoading && !showResult && (
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Analyzing your texting DNA... 🔍</Text>
          <View style={styles.loadingBarContainer}>
            <Animated.View
              style={[
                styles.loadingBar,
                { width: loadingWidth }
              ]}
            />
          </View>
        </View>
      )}

      {showResult && (
        <Animated.View style={[styles.resultContainer, { opacity: resultFadeAnim }]}>
          <Text style={styles.resultTitle}>Your Texting Personality</Text>

          <View style={[styles.personalityCard, { backgroundColor: personalityResult.color }]}>
            <Text style={styles.personalityTitle}>{personalityResult.title}</Text>
            <Text style={styles.personalityDescription}>{personalityResult.description}</Text>
          </View>

          <TouchableOpacity
            style={styles.continueButton}
            onPress={handleContinue}
            activeOpacity={0.8}
          >
            <Text style={styles.continueButtonText}>Unlock Premium Features</Text>
            <MaterialIcons name="arrow-forward" size={20} color="#fff" />
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  container: {
    flex: 1,
    position: 'relative',
    // Ensure content is above the gradient
    backgroundColor: 'transparent', // Make container transparent
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: -1, // Ensure it's behind other content
  },
  questionContainer: {
    flex: 1,
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: StatusBar.currentHeight + 40, // Ensure content starts below status bar
    paddingBottom: 20,
  },
  progressContainer: {
    flexDirection: 'row',
    marginBottom: 30,
    // Removed paddingTop here, handled by questionContainer
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#333',
    marginHorizontal: 4,
  },
  progressDotActive: {
    backgroundColor: '#00F2EA',
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  questionNumber: {
    color: '#00F2EA',
    fontSize: 16,
    marginBottom: 10,
  },
  questionText: {
    fontSize: 26, // Increased size
    fontWeight: 'bold',
    // color: '#FFFFFF', // Use theme color
    textAlign: 'center',
    marginBottom: 30, // Increased spacing
    paddingHorizontal: 20,
  },
  // Updated options container to ensure all options are visible
  optionsContainer: {
    width: '100%',
    maxHeight: height * 0.5, // Set max height to 50% of screen height
    flexGrow: 0, // Prevent ScrollView from expanding beyond its content
  },
  // Add content container style for ScrollView
  optionsContentContainer: {
    paddingBottom: 20, // Add padding at the bottom
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: '#2a2a2a', // Use theme color
    paddingVertical: 18, // Increased padding
    paddingHorizontal: 20,
    borderRadius: 15, // More rounded
    marginBottom: 15, // Increased spacing
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)', // Subtle border
  },
  optionIcon: {
    fontSize: 24,
    marginRight: 15,
    // color: '#aaa', // Use theme color
  },
  optionCard: { // Renamed from optionButton for clarity
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)', // Semi-transparent white
    paddingVertical: 18,
    paddingHorizontal: 20,
    borderRadius: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  optionIcon: {
    fontSize: 24,
    marginRight: 15,
    // color: '#FFFFFF', // Handled by custom Text component
  },
  optionText: {
    fontSize: 17,
    // color: '#FFFFFF', // Handled by custom Text component
    flex: 1,
  },
  // Add padding at the bottom of options
  optionsPadding: {
    height: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  loadingText: {
    fontSize: 20,
    fontWeight: '600',
    // color: '#FFFFFF', // Handled by custom Text component
    marginBottom: 25,
    textAlign: 'center',
  },
  loadingBarContainer: {
    width: '80%',
    height: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    overflow: 'hidden',
  },
  loadingBar: {
    height: '100%',
    backgroundColor: '#00F2EA', // Use a theme color or accent
    borderRadius: 4,
  },
  // progressBarBackground defined above
  // progressBar defined above
  resultContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    opacity: 0, // Initial state for animation
  },
  resultCard: {
    width: '100%',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
  },
  resultEmoji: {
    fontSize: 60,
    marginBottom: 15,
  },
  resultTitle: {
    fontSize: 24, // Adjusted size
    fontWeight: 'bold',
    // color: '#FFFFFF', // Handled by custom Text component
    marginBottom: 20, // Increased spacing
    textAlign: 'center',
  },
  personalityCard: {
    width: '90%',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
  },
  personalityTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#FFFFFF', // Keep white for contrast on colored card
    marginBottom: 10,
    textAlign: 'center',
  },
  personalityDescription: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)', // Keep white for contrast
    textAlign: 'center',
    lineHeight: 24,
  },
  resultDescription: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#00F2EA', // Use accent color
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 30,
    shadowColor: '#00F2EA',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 8,
  },
  continueButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    marginRight: 10,
  },
});
