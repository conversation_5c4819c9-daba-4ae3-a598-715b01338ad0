/**
 * Beautiful, wavy mesh-like gradients for the onboarding quiz
 * Each question will have its own unique gradient inspired by the reference dark gradients
 * Designed to create organic, flowing color transitions similar to the reference images
 */

import { GradientType } from './Gradients';

// Helper function to create complex mesh-like gradient transitions
const createMeshGradient = (
  colors: string[],
  start: { x: number; y: number } = { x: 0.2, y: 0.1 },
  end: { x: number; y: number } = { x: 0.8, y: 0.9 },
  locations?: number[]
): GradientType => {
  // If locations aren't provided, create non-linear distributed locations
  // This creates more organic, wavy transitions between colors
  const calculatedLocations = locations ||
    colors.map((_, index) => {
      // Create non-linear distribution for more organic transitions
      const position = index / (colors.length - 1);
      // Apply slight curve to the distribution
      return Math.pow(position, 1.2);
    });

  return {
    id: Math.floor(Math.random() * 1000) + 100, // Random ID for the gradient
    name: 'Mesh Gradient', // Name for the gradient
    colors,
    start,
    end,
    locations: calculatedLocations,
  };
};

// Legacy function for backward compatibility
const createSmoothGradient = createMeshGradient;

// Question 1: Vibrant purple mesh gradient inspired by reference #104
export const question1Gradient = createMeshGradient(
  [
    '#000000', // Pure black (edge)
    '#1A0838', // Very dark purple
    '#4A1E80', // Deep purple
    '#6B2FBF', // Medium purple
    '#9D4EDD', // Bright purple
    '#C77DFF', // Light purple
    '#E0AAFF', // Very light purple
    '#F3D9FA', // Almost white purple
    '#1A0838', // Return to dark purple (creates wave effect)
    '#000000'  // Pure black (edge)
  ],
  { x: 0.2, y: 0.1 },
  { x: 0.8, y: 0.9 },
  [0, 0.1, 0.2, 0.3, 0.5, 0.6, 0.7, 0.8, 0.9, 1]
);

// Question 2: Vibrant red-orange mesh gradient inspired by reference #94
export const question2Gradient = createMeshGradient(
  [
    '#000000', // Pure black (edge)
    '#1F0A05', // Very dark red
    '#4D1007', // Deep red
    '#8B0000', // Dark red
    '#DC143C', // Crimson
    '#FF4500', // Orange-red
    '#FF8C00', // Dark orange
    '#FFA07A', // Light salmon
    '#380808', // Return to dark red (creates wave effect)
    '#000000'  // Pure black (edge)
  ],
  { x: 0.1, y: 0.2 },
  { x: 0.9, y: 0.8 },
  [0, 0.1, 0.2, 0.3, 0.4, 0.6, 0.7, 0.8, 0.9, 1]
);

// Question 3: Vibrant blue mesh gradient inspired by reference #82
export const question3Gradient = createMeshGradient(
  [
    '#000000', // Pure black (edge)
    '#051028', // Very dark blue
    '#0A1A40', // Deep blue
    '#0047AB', // Cobalt blue
    '#1E90FF', // Dodger blue
    '#00BFFF', // Deep sky blue
    '#87CEEB', // Sky blue
    '#E0F7FA', // Very light blue
    '#081638', // Return to dark blue (creates wave effect)
    '#000000'  // Pure black (edge)
  ],
  { x: 0.3, y: 0.1 },
  { x: 0.7, y: 0.9 },
  [0, 0.1, 0.2, 0.3, 0.5, 0.6, 0.7, 0.8, 0.9, 1]
);

// Question 4: Vibrant teal-cyan mesh gradient inspired by reference #68
export const question4Gradient = createMeshGradient(
  [
    '#000000', // Pure black (edge)
    '#052820', // Very dark teal
    '#004D40', // Deep teal
    '#008080', // Teal
    '#20B2AA', // Light sea green
    '#40E0D0', // Turquoise
    '#7FFFD4', // Aquamarine
    '#E0FFFF', // Light cyan
    '#083830', // Return to dark teal (creates wave effect)
    '#000000'  // Pure black (edge)
  ],
  { x: 0.2, y: 0.2 },
  { x: 0.8, y: 0.8 },
  [0, 0.1, 0.2, 0.3, 0.5, 0.6, 0.7, 0.8, 0.9, 1]
);

// Question 5: Vibrant magenta-pink mesh gradient inspired by reference #49
export const question5Gradient = createMeshGradient(
  [
    '#000000', // Pure black (edge)
    '#280528', // Very dark magenta
    '#4B0082', // Indigo
    '#8A2BE2', // Blue violet
    '#9932CC', // Dark orchid
    '#C71585', // Medium violet red
    '#FF00FF', // Magenta
    '#FF69B4', // Hot pink
    '#380838', // Return to dark magenta (creates wave effect)
    '#000000'  // Pure black (edge)
  ],
  { x: 0.1, y: 0.3 },
  { x: 0.9, y: 0.7 },
  [0, 0.1, 0.2, 0.3, 0.4, 0.6, 0.7, 0.8, 0.9, 1]
);

// Question 6: Vibrant orange-gold mesh gradient inspired by reference #89
export const question6Gradient = createMeshGradient(
  [
    '#000000', // Pure black (edge)
    '#281005', // Very dark orange
    '#5C2C06', // Deep orange-brown
    '#8B4513', // Saddle brown
    '#D2691E', // Chocolate
    '#FF8C00', // Dark orange
    '#FFA500', // Orange
    '#FFD700', // Gold
    '#381A08', // Return to dark orange (creates wave effect)
    '#000000'  // Pure black (edge)
  ],
  { x: 0.2, y: 0.3 },
  { x: 0.8, y: 0.7 },
  [0, 0.1, 0.2, 0.3, 0.4, 0.6, 0.7, 0.8, 0.9, 1]
);

// Question 7: Vibrant green mesh gradient inspired by reference #28
export const question7Gradient = createMeshGradient(
  [
    '#000000', // Pure black (edge)
    '#05280F', // Very dark green
    '#006400', // Dark green
    '#228B22', // Forest green
    '#32CD32', // Lime green
    '#7CFC00', // Lawn green
    '#ADFF2F', // Green yellow
    '#F0FFF0', // Honeydew
    '#083816', // Return to dark green (creates wave effect)
    '#000000'  // Pure black (edge)
  ],
  { x: 0.3, y: 0.2 },
  { x: 0.7, y: 0.8 },
  [0, 0.1, 0.2, 0.3, 0.5, 0.6, 0.7, 0.8, 0.9, 1]
);

// Get gradient for a specific question index (0-based)
export const getQuestionGradient = (questionIndex: number): GradientType => {
  const gradients = [
    question1Gradient,
    question2Gradient,
    question3Gradient,
    question4Gradient,
    question5Gradient,
    question6Gradient,
    question7Gradient,
  ];

  // Return the gradient for the question index, or the first gradient if out of bounds
  return gradients[questionIndex] || gradients[0];
};
