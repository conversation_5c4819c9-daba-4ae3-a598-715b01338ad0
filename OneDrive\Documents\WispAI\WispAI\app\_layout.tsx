// Import polyfills first to ensure Node.js modules are handled properly
import '../polyfills';

import React from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { PaperProvider } from 'react-native-paper';
import { ActivityIndicator, StyleSheet } from 'react-native';
import Text from '../components/common/Text';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { LinearGradient } from 'expo-linear-gradient';
import { AuthProvider } from '../contexts/AuthContext';
import { ThemeProvider } from '../contexts/ThemeContext';
import { NotificationProvider } from '../contexts/NotificationContext';
import { useFonts } from '../utils/useFonts';
import { initializeAnalytics } from '../lib/analytics';
import { initializeCache } from '../lib/cache';
import { initializeNetworkMonitoring } from '../lib/network';
import { initializeDatabaseTables } from '../lib/database';
import { loadFonts } from '../utils/loadFonts';

// Improved initialization with proper error handling
const actualInitialization = async () => {
  try {
    // Initialize services one by one with error handling
    console.log('Initializing network monitoring...');
    initializeNetworkMonitoring();

    console.log('Initializing analytics...');
    await initializeAnalytics();

    console.log('Initializing cache...');
    await initializeCache();

    console.log('Initializing database tables...');
    await initializeDatabaseTables();

    console.log('Loading custom fonts...');
    await loadFonts();

    console.log('All services initialized successfully');
    return true;
  } catch (error) {
    console.error('Error during initialization:', error);
    // Continue despite errors - don't block app startup
    return true;
  }
};

const initializeWithTimeout = async (timeoutMs = 10000) => {
  return Promise.race([
    actualInitialization(),
    new Promise((resolve) =>
      // Use resolve instead of reject to avoid crashing
      setTimeout(() => {
        console.warn('Initialization timed out, continuing anyway');
        resolve(true);
      }, timeoutMs)
    )
  ]);
};

function AppContent() {
  // Use our custom hook to load fonts
  const fontsLoaded = useFonts();
  const [initialized, setInitialized] = React.useState(false);

  // Use a specific gradient that fades to black at the bottom
  // Using a smoother, more blended purple gradient that transitions to black
  const loadingGradient = {
    colors: ['#6B2FBF', '#9A45D1', '#7A35C1', '#4A1E80', '#2A1050', '#150828', '#000000'],
    start: { x: 0.2, y: 0 },  // Start from left side of top
    end: { x: 0.8, y: 1 },    // End at right side of bottom (creates diagonal effect)
    locations: [0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.7], // Many intermediate steps for smoother blend
  };

  // Initialize app services after fonts are loaded
  React.useEffect(() => {
    if (!fontsLoaded) return;

    const initApp = async () => {
      try {
        await initializeWithTimeout();
        console.log('App successfully initialized');
      } catch (error) {
        console.error('Failed to initialize app:', error);
      } finally {
        // Always set initialized to true to allow the app to continue
        setInitialized(true);
      }
    };

    initApp();
  }, [fontsLoaded]);

  // If fonts aren't loaded or initialization isn't complete, show loading screen
  if (!fontsLoaded || !initialized) {
    return (
      <LinearGradient
        colors={loadingGradient.colors as any}
        start={loadingGradient.start}
        end={loadingGradient.end}
        locations={loadingGradient.locations as any}
        style={styles.loadingContainer}
      >
        <ActivityIndicator size="large" color="#00E5FF" />
        <Text
          variant="h3"
          color="#00E5FF"
          style={[styles.loadingText, { fontFamily: 'Satoshi-Regular' }]}
        >
          Loading WispAI...
        </Text>
      </LinearGradient>
    );
  }

  return (
    <PaperProvider>
      <StatusBar style="light" />
      <Stack
        screenOptions={{
          headerStyle: {
            backgroundColor: '#0A0A0A', // Jet black matte (60% - Primary/Base)
          },
          headerTintColor: '#00E5FF', // Vivid electric cyan (10% - Highlight Accent)
          headerTitleStyle: {
            fontWeight: 'bold',
          },
          contentStyle: {
            backgroundColor: '#0A0A0A', // Jet black matte (60% - Primary/Base)
          },
        }}
      >
        <Stack.Screen name="index" options={{ headerShown: false }} />
        <Stack.Screen name="(auth)" options={{ headerShown: false }} />
        <Stack.Screen name="(main)" options={{ headerShown: false }} />
        <Stack.Screen name="(onboarding)" options={{ headerShown: false }} />
      </Stack>
    </PaperProvider>
  );
}

export default function RootLayout() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ThemeProvider>
        <AuthProvider>
          <NotificationProvider>
            <AppContent />
          </NotificationProvider>
        </AuthProvider>
      </ThemeProvider>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    marginTop: 10,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },
});