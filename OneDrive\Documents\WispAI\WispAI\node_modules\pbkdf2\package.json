{"name": "pbkdf2", "version": "3.0.8", "description": "This library provides the functionality of PBKDF2 with the ability to use any supported hashing algorithm returned from crypto.getHashes()", "keywords": ["pbkdf2", "kdf", "salt", "hash"], "homepage": "https://github.com/crypto-browserify/pbkdf2", "bugs": {"url": "https://github.com/crypto-browserify/pbkdf2/issues"}, "license": "MIT", "author": "<PERSON>", "browser": "browser.js", "files": ["browser.js", "index.js", "node-shim-async.js", "node-shim.js", "precondition.js"], "main": "index.js", "repository": {"type": "git", "url": "https://github.com/crypto-browserify/pbkdf2.git"}, "scripts": {"prepublish": "npm run test", "coverage": "nyc --check-coverage --branches 90 --functions 100 tape test/*.js", "lint": "standard", "test": "npm run lint && npm run unit", "bundle-test": "browserify test/index.js > test/bundle.js", "unit": "tape test/*.js"}, "devDependencies": {"browserify": "*", "nyc": "^6.4.0", "standard": "*", "tape": "^4.5.1"}, "dependencies": {"create-hmac": "^1.1.2"}, "standard": {"ignore": ["test/bundle.js"]}, "engines": {"node": ">=0.12"}}