'use strict';

const WebSocket = require('./lib/websocket');

// React Native polyfill: WebSocket streams are not supported
WebSocket.createWebSocketStream = function() {
  console.warn('WebSocket streams are not supported in React Native');
  return {
    on: function() {},
    emit: function() {},
    write: function() {},
    end: function() {},
    destroy: function() {},
    pipe: function() { return this; },
    unpipe: function() { return this; },
  };
};
// React Native polyfill: Use a no-op WebSocket server for development
WebSocket.Server = class WebSocketServer {
  constructor(options) {
    // Allow Expo development server to work, but warn for app usage
    if (process.env.NODE_ENV === 'development') {
      console.warn('WebSocket server created in development mode');
      // Create a minimal server that doesn't actually do anything
      this.clients = new Set();
      this.options = options || {};
    } else {
      throw new Error('WebSocket servers are not supported in React Native apps');
    }
  }

  on() { return this; }
  emit() { return this; }
  close() { return this; }
  handleUpgrade() { return this; }
  address() { return null; }
};
WebSocket.Receiver = require('./lib/receiver');
WebSocket.Sender = require('./lib/sender');

module.exports = WebSocket;
