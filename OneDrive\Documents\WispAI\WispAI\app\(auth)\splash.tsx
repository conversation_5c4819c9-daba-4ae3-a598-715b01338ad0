import { useEffect, useRef, useState } from 'react';
import {
  View,
  Text as RNText,
  StyleSheet,
  Animated,
  Dimensions,
  StatusBar,
  Easing,
} from 'react-native';
import { useRouter, useNavigation } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import Text from '../../components/common/Text';

// Define the Satoshi Regular font family name
const satoshiRegular = 'Satoshi-Regular';

const { width, height } = Dimensions.get('window');

export default function SplashScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  const [showMessage, setShowMessage] = useState(false);
  const [fadeToBlack, setFadeToBlack] = useState(false);

  // Use a specific gradient (gradient 33 - Deep purple to blue to cyan)
  // This gradient will be positioned at the top, fading to black at the bottom
  // Using a smoother, more blended purple gradient that transitions to black
  const splashGradient = {
    // Even more color steps for an ultra-smooth blend from purple to black
    colors: [
      '#6B2FBF', // Bright purple
      '#9A45D1', // Medium purple
      '#7A35C1', // Purple
      '#5A2590', // Dark purple
      '#4A1E80', // Deeper purple
      '#3A1670', // Very deep purple
      '#2A1050', // Almost black purple
      '#1A0838', // Very dark purple
      '#150828', // Nearly black purple
      '#100618', // Extremely dark purple
      '#080310', // Almost black
      '#000000'  // Pure black
    ],
    start: { x: 0.2, y: 0 },  // Start from left side of top
    end: { x: 0.8, y: 1 },    // End at right side of bottom (creates diagonal effect)
    // More location steps for smoother gradient transition
    locations: [0, 0.05, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35, 0.4, 0.5, 0.6, 0.7],
  };

  // Set empty header title on mount
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
      title: '',
    });
  }, [navigation]);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const logoScaleAnim = useRef(new Animated.Value(0.9)).current;
  const logoOpacityAnim = useRef(new Animated.Value(0)).current;
  const loaderWidthAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const textOpacityAnim = useRef(new Animated.Value(0)).current;
  const messageFadeAnim = useRef(new Animated.Value(0)).current;
  const blackOverlayAnim = useRef(new Animated.Value(0)).current;

  // Wave animation values for more natural gradient effect
  const wave1Anim = useRef(new Animated.Value(0)).current;
  const wave2Anim = useRef(new Animated.Value(0)).current;

  // Particle animations
  const particles = Array(15).fill(0).map(() => ({
    x: useRef(new Animated.Value(Math.random() * width)).current,
    y: useRef(new Animated.Value(Math.random() * height)).current,
    size: Math.random() * 3 + 1,
    opacity: useRef(new Animated.Value(Math.random() * 0.5)).current,
  }));

  // Interpolate loader width
  const loaderWidth = loaderWidthAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  useEffect(() => {
    // Start animations
    const animateParticles = () => {
      particles.forEach((particle, i) => {
        Animated.parallel([
          Animated.timing(particle.x, {
            toValue: Math.random() * width,
            duration: 15000 + (i * 1000),
            useNativeDriver: true,
          }),
          Animated.timing(particle.y, {
            toValue: Math.random() * height,
            duration: 20000 + (i * 1000),
            useNativeDriver: true,
          }),
          Animated.sequence([
            Animated.timing(particle.opacity, {
              toValue: Math.random() * 0.2 + 0.1,
              duration: 5000,
              useNativeDriver: true,
            }),
            Animated.timing(particle.opacity, {
              toValue: Math.random() * 0.3,
              duration: 5000,
              useNativeDriver: true,
            }),
          ]),
        ]).start(() => {
          // Reset particle position when animation completes
          particle.x.setValue(Math.random() * width);
          particle.y.setValue(Math.random() * height);
        });
      });
    };

    // Background fade in
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    // Start particle animations
    animateParticles();

    // Animate wave effects for more natural gradient
    Animated.loop(
      Animated.sequence([
        Animated.timing(wave1Anim, {
          toValue: 1,
          duration: 15000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: false,
        }),
        Animated.timing(wave1Anim, {
          toValue: 0,
          duration: 15000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: false,
        }),
      ])
    ).start();

    Animated.loop(
      Animated.sequence([
        Animated.timing(wave2Anim, {
          toValue: 1,
          duration: 20000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: false,
        }),
        Animated.timing(wave2Anim, {
          toValue: 0,
          duration: 20000,
          easing: Easing.inOut(Easing.sin),
          useNativeDriver: false,
        }),
      ])
    ).start();

    // Logo animation sequence
    Animated.sequence([
      // Wait a bit before showing logo
      Animated.delay(500),

      // Fade in and scale logo
      Animated.parallel([
        Animated.timing(logoOpacityAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.spring(logoScaleAnim, {
          toValue: 1,
          friction: 8,
          tension: 40,
          useNativeDriver: true,
        }),
      ]),

      // Start pulsing animation for logo
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ]),
      ),

      // Fade in text after logo appears
      Animated.timing(textOpacityAnim, {
        toValue: 1,
        duration: 1000,
        delay: 500,
        useNativeDriver: true,
      }),

      // Start loader animation
      Animated.timing(loaderWidthAnim, {
        toValue: 1,
        duration: 3000,
        useNativeDriver: false,
      }),

    ]).start();

    // Show message after 3 seconds
    setTimeout(() => {
      setShowMessage(true);

      // Animate message fade in
      Animated.timing(messageFadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();

      // Hide message after 5 seconds
      setTimeout(() => {
        Animated.timing(messageFadeAnim, {
          toValue: 0,
          duration: 500,
          useNativeDriver: true,
        }).start(() => {
          setShowMessage(false);
        });
      }, 5000);
    }, 3000);

    // Navigate to onboarding after splash screen with fade to black
    const timer = setTimeout(() => {
      // Start fade to black animation
      setFadeToBlack(true);
      Animated.timing(blackOverlayAnim, {
        toValue: 1,
        duration: 1000, // 1 second fade to black
        useNativeDriver: true,
      }).start(() => {
        // Navigate after fade to black completes
        router.replace('/(auth)/onboarding');
      });
    }, 9000); // Start fade at 9 seconds for a total of 10 seconds

    return () => clearTimeout(timer);
  }, []);

  return (
    <Animated.View style={[styles.container]}>
      <StatusBar barStyle="light-content" />

      {/* Gradient Background - Fades from gradient at top to black at bottom with wave effect */}
      <Animated.View style={styles.background}>
        <LinearGradient
          colors={splashGradient.colors as any}
          start={splashGradient.start}
          end={splashGradient.end}
          locations={splashGradient.locations as any}
          style={StyleSheet.absoluteFillObject}
        >
          {/* Animated particles */}
          {particles.map((particle, index) => (
            <Animated.View
              key={index}
              style={[
                styles.particle,
                {
                  width: particle.size,
                  height: particle.size,
                  opacity: particle.opacity,
                  transform: [
                    { translateX: particle.x },
                    { translateY: particle.y },
                  ],
                },
              ]}
            />
          ))}
        </LinearGradient>
      </Animated.View>

      {/* Logo */}
      <Animated.View
        style={[
          styles.logoContainer,
          {
            opacity: logoOpacityAnim,
            transform: [
              { scale: Animated.multiply(logoScaleAnim, pulseAnim) },
            ],
          },
        ]}
      >
        <View style={styles.logoCircle}>
          <View style={styles.logoInnerCircle} />
          <View style={styles.logoRing} />
          <View style={styles.logoRingOuter} />

          {/* Radar lines */}
          <View style={[styles.radarLine, { transform: [{ rotate: '0deg' }] }]} />
          <View style={[styles.radarLine, { transform: [{ rotate: '45deg' }] }]} />
          <View style={[styles.radarLine, { transform: [{ rotate: '90deg' }] }]} />
          <View style={[styles.radarLine, { transform: [{ rotate: '135deg' }] }]} />
        </View>
      </Animated.View>

      {/* App title and tagline */}
      <View style={styles.titleContainer}>
        <Animated.View
          style={{
            opacity: 1, // Force full opacity for testing
          }}
        >
          <Text style={styles.title}>
            WispAI
          </Text>
        </Animated.View>

        <Animated.View
          style={{
            opacity: 1, // Force full opacity for testing
          }}
        >
          <Text style={styles.tagline}>
            Understand the vibe before they ghost.
          </Text>
        </Animated.View>
      </View>

      {/* Loading bar */}
      <Animated.View style={styles.loaderContainer}>
        <Animated.View
          style={[
            styles.loader,
            {
              width: loaderWidth,
            },
          ]}
        />
      </Animated.View>

      {/* Fake message */}
      {showMessage && (
        <Animated.View style={[styles.messageContainer, { opacity: messageFadeAnim }]}>
          <View style={styles.message}>
            <Text style={styles.messageText}>Sorry... I've just been busy.</Text>
          </View>
          <Text style={styles.deliveredText}>
            Delivered 3 days ago
          </Text>
        </Animated.View>
      )}

      {/* Gradient overlay for fade transition */}
      {fadeToBlack && (
        <Animated.View style={{ opacity: blackOverlayAnim, ...StyleSheet.absoluteFillObject, zIndex: 999 }}>
          <LinearGradient
            colors={['#000000', '#0A0A0A', '#000000']}
            style={styles.blackOverlay}
          />
        </Animated.View>
      )}
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
    alignItems: 'center',
    justifyContent: 'center',
  },
  background: {
    ...StyleSheet.absoluteFillObject,
  },
  particle: {
    position: 'absolute',
    backgroundColor: '#00F2EA',
    borderRadius: 50,
    opacity: 0.6, // Further increased opacity for better visibility against gradient
    shadowColor: '#00F2EA',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 8,
  },
  logoContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    position: 'absolute',
    top: height * 0.25, // Move up even higher on the screen
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  logoInnerCircle: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#00F2EA',
    shadowColor: '#00F2EA',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 10,
    elevation: 10,
  },
  logoRing: {
    position: 'absolute',
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#00F2EA',
    opacity: 0.7,
  },
  logoRingOuter: {
    position: 'absolute',
    width: 70,
    height: 70,
    borderRadius: 35,
    borderWidth: 1,
    borderColor: '#00F2EA',
    opacity: 0.4,
  },
  radarLine: {
    position: 'absolute',
    width: '100%',
    height: 1,
    backgroundColor: '#00F2EA',
    opacity: 0.3,
  },
  titleContainer: {
    position: 'absolute',
    top: height * 0.45, // Position lower on the screen
    alignItems: 'center',
    justifyContent: 'center',
    width: width * 0.9, // Make sure it has enough width
  },
  title: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#00F2EA',
    letterSpacing: 2,
    textShadowColor: '#00F2EA',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 15,
    marginBottom: 10, // Reduced margin to bring tagline closer
    fontFamily: satoshiRegular,
    lineHeight: 60, // Added line height to prevent clipping
  },
  tagline: {
    fontSize: 15,
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 30,
    fontWeight: '500',
    textShadowColor: '#00F2EA',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 6,
    letterSpacing: 0.5,
    fontFamily: satoshiRegular,
    lineHeight: 22, // Added line height to prevent clipping
  },
  loaderContainer: {
    width: width * 0.7,
    height: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 1,
    overflow: 'hidden',
    position: 'absolute',
    bottom: 50,
  },
  loader: {
    height: '100%',
    backgroundColor: '#00F2EA',
    borderRadius: 1,
  },
  messageContainer: {
    alignItems: 'center',
    position: 'absolute',
    top: height * 0.75, // Position lower on the screen
    width: width * 0.9,
    zIndex: 10,
  },
  message: {
    backgroundColor: '#222',
    padding: 15,
    borderRadius: 20,
    width: '100%',
    marginBottom: 10,
    borderWidth: 1,
    borderColor: '#00E5FF',
    shadowColor: '#00E5FF',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 15,
    elevation: 10,
  },
  messageText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 229, 255, 0.5)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 10,
    fontFamily: satoshiRegular,
    lineHeight: 26, // Added line height to prevent clipping
  },
  deliveredText: {
    color: '#ccc',
    fontSize: 14,
    marginTop: 5,
    textAlign: 'right',
    width: '90%',
    fontWeight: '500',
    fontFamily: satoshiRegular,
    lineHeight: 20, // Added line height to prevent clipping
  },
  blackOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000000',
    zIndex: 100,
  },
});
