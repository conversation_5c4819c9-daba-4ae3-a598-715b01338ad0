// React Native polyfill for ws/lib/stream.js
// This file replaces the original stream.js to prevent Node.js module import errors

module.exports = {
  createWebSocketStream: function() {
    console.warn('WebSocket streams are not supported in React Native');
    return {
      on: function() {},
      emit: function() {},
      write: function() {},
      end: function() {},
      destroy: function() {},
    };
  }
};
