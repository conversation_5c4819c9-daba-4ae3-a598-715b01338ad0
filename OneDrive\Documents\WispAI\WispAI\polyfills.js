/**
 * Polyfills for Node.js modules that don't work in React Native
 * This file provides empty implementations or React Native alternatives
 */

// Polyfill for 'stream' module
try {
  if (typeof global.stream === 'undefined') {
    global.stream = require('react-native-stream');
  }
} catch (e) {
  // Fallback if react-native-stream is not available
  global.stream = {
    Duplex: class Duplex {},
    Readable: class Readable {},
    Writable: class Writable {},
    Transform: class Transform {},
    PassThrough: class PassThrough {},
  };
}

// Polyfill for 'crypto' module
const cryptoPolyfill = {
  randomBytes: function(size) {
    const array = new Uint8Array(size);
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
      crypto.getRandomValues(array);
    } else {
      // Fallback for environments without crypto.getRandomValues
      for (let i = 0; i < size; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return array;
  },
  createHash: function(algorithm) {
    console.warn(`Crypto hash ${algorithm} is not supported in React Native`);
    return {
      update: function() { return this; },
      digest: function() { return ''; }
    };
  },
  createHmac: function(algorithm, key) {
    console.warn(`Crypto HMAC ${algorithm} is not supported in React Native`);
    return {
      update: function() { return this; },
      digest: function() { return ''; }
    };
  }
};

global.crypto = cryptoPolyfill;

// Polyfill for Node.js modules that should not be used in React Native
const noopModule = () => {
  console.warn('This Node.js module is not available in React Native');
  return {};
};

const noopClass = class NoopClass {
  constructor() {
    console.warn('This Node.js class is not available in React Native');
  }
};

// Create no-op implementations for Node.js modules
global.http = noopModule;
global.https = noopModule;
global.net = noopModule;
global.tls = noopModule;
global.fs = noopModule;
global.path = noopModule;
global.os = noopModule;
global.util = noopModule;
global.events = { EventEmitter: noopClass };
global.buffer = { Buffer: noopClass };
global.url = noopModule;
global.querystring = noopModule;

// Comprehensive polyfill for 'ws' package
const wsPolyfill = {
  // Main WebSocket class
  WebSocket: class WebSocket {
    constructor() {
      console.warn('WebSocket from ws package is not available in React Native. Use the native WebSocket API instead.');
      return {};
    }
  },

  // WebSocket Server (not available in React Native)
  WebSocketServer: class WebSocketServer {
    constructor() {
      console.warn('WebSocketServer is not available in React Native');
      return {};
    }
  },

  // Stream creation function
  createWebSocketStream: () => {
    console.warn('WebSocket streams are not supported in React Native');
    return {};
  },

  // Constants
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
};

// Export the polyfill as both default and named exports
module.exports = wsPolyfill;
module.exports.default = wsPolyfill;
module.exports.WebSocket = wsPolyfill.WebSocket;
module.exports.WebSocketServer = wsPolyfill.WebSocketServer;
module.exports.createWebSocketStream = wsPolyfill.createWebSocketStream;

// Also export crypto polyfill
module.exports.crypto = cryptoPolyfill;
module.exports.randomBytes = cryptoPolyfill.randomBytes;
module.exports.createHash = cryptoPolyfill.createHash;
module.exports.createHmac = cryptoPolyfill.createHmac;

// Set global references
global.ws = wsPolyfill;

console.log('React Native polyfills loaded successfully');
