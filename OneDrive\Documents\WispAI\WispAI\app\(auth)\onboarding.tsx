import React, { useState, useEffect, useRef } from 'react';

// Define the Satoshi Regular font family name
const satoshiRegular = 'Satoshi-Regular';
import {
  View,
  Text as RNText, // Import standard Text as RNText
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  Animated,
  StatusBar,
} from 'react-native';
import { useRouter, useNavigation } from 'expo-router';
import { MaterialIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';
// Import LinearGradient
import { LinearGradient } from 'expo-linear-gradient';
// Import useTheme
import { useTheme } from '../../contexts/ThemeContext';
// Import custom Text component
import Text from '../../components/common/Text';
// No need to import gradient functions anymore

const { width, height } = Dimensions.get('window');

// Quiz questions and options
const quizQuestions = [
  {
    question: 'How do you usually start a convo?',
    options: [
      { id: 'a', text: '"Hey 😊"', icon: '👋' },
      { id: 'b', text: 'Meme or TikTok link', icon: '🎬' },
      { id: 'c', text: 'Deep thought at 2am', icon: '🌙' },
      { id: 'd', text: 'I wait for them to text first', icon: '⏱️' },
    ],
  },
  {
    question: 'What\'s your texting vibe?',
    options: [
      { id: 'a', text: 'Dry but loyal', icon: '🐢' },
      { id: 'b', text: 'Overthink every message', icon: '🤯' },
      { id: 'c', text: 'Smooth & flirty', icon: '😏' },
      { id: 'd', text: 'Left on read... by me 😌', icon: '🧊' },
    ],
  },
  {
    question: 'What\'s your biggest texting ick?',
    options: [
      { id: 'a', text: 'One-word replies', icon: '😒' },
      { id: 'b', text: 'Typing... then nothing', icon: '💬' },
      { id: 'c', text: 'Random ghosting', icon: '👻' },
      { id: 'd', text: '"k" 😐', icon: '🙄' },
    ],
  },
  {
    question: 'What does ghosting feel like to you?',
    options: [
      { id: 'a', text: 'Confusing', icon: '❓' },
      { id: 'b', text: 'Normal', icon: '🤷' },
      { id: 'c', text: 'Kind of funny', icon: '😆' },
      { id: 'd', text: 'Lowkey heartbreaking', icon: '💔' },
    ],
  },
  {
    question: 'When someone takes hours to reply, you...',
    options: [
      { id: 'a', text: 'Wait it out', icon: '⏳' },
      { id: 'b', text: 'Double-text 😬', icon: '📱' },
      { id: 'c', text: 'Do the same back', icon: '🪞' },
      { id: 'd', text: 'Unfollow. Next.', icon: '👋' },
    ],
  },
  {
    question: 'What are you hoping WispAI will do for you?',
    options: [
      { id: 'a', text: 'Decode mixed signals', icon: '🔍' },
      { id: 'b', text: 'Help me respond better', icon: '💬' },
      { id: 'c', text: 'Tell me if I\'m being ghosted', icon: '👻' },
      { id: 'd', text: 'Just for fun 👀', icon: '🎮' },
    ],
  },
  {
    question: 'What\'s your texting survival instinct?',
    options: [
      { id: 'a', text: 'Ghost them first 😤', icon: '🏃' },
      { id: 'b', text: 'Pretend I\'m unbothered (I\'m not)', icon: '😎' },
      { id: 'c', text: 'Analyze every word like a crime scene 🕵️', icon: '🔎' },
      { id: 'd', text: 'Hope for the best and text again anyway 💌', icon: '✨' },
    ],
  },
];

// Personality types based on quiz results
const personalityTypes = [
  {
    id: 'ghost_magnet',
    title: 'Ghost Magnet 👻',
    description: 'You attract the emotionally unavailable like moths to a flame. You\'re sweet, responsive, and maybe a little too forgiving. We\'re here to protect your heart.',
    color: '#E57373', // Light red
  },
  {
    id: 'overthinker',
    title: 'Overthinker 3000 🤯',
    description: 'You reread messages 8 times and analyze every emoji. You don\'t miss a beat — and neither does WispAI. Let\'s overanalyze together.',
    color: '#64B5F6', // Light blue
  },
  {
    id: 'idgaf',
    title: 'The IDGAF Texter 🧊',
    description: 'You\'re detached, mysterious, and always in control. But deep down? You kinda want to care. WispAI can decode what they really meant.',
    color: '#9575CD', // Light purple
  },
  {
    id: 'romantic',
    title: 'Hopeful Romantic 💌',
    description: 'You\'re in it for the real thing. You believe in vibes, late-night convos, and subtle flirting. WispAI helps you tell the real from the fake.',
    color: '#F06292', // Light pink
  },
  {
    id: 'flirt',
    title: 'The Flirt Bot 😏',
    description: 'You\'re a natural. Texting is your love language — but even pros can miss a red flag. WispAI keeps your game sharp.',
    color: '#FF8A65', // Light orange
  },
  {
    id: 'dry_texter',
    title: 'Dry Texter, Loyal Heart 🐢',
    description: 'You might not text novels, but you mean every word. WispAI helps make your short replies still hit deep.',
    color: '#4DB6AC', // Light teal
  },
  {
    id: 'mirror',
    title: 'The Mirror 🪞',
    description: 'You match energy. Quick replies? You\'re quick too. Ignored? Bye. But sometimes it\'s hard to tell what they\'re really feeling — that\'s where we come in.',
    color: '#FFD54F', // Light amber
  },
  {
    id: 'casual',
    title: 'Casual Until It\'s Serious 😎',
    description: 'You keep it chill until things get real. But when they do, you want clarity. WispAI helps you catch the shift before it happens.',
    color: '#81C784', // Light green
  },
];

export default function OnboardingScreen() {
  const router = useRouter();
  const navigation = useNavigation();
  // We use theme context for text colors
  useTheme();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [showResult, setShowResult] = useState(false);
  const [personalityResult, setPersonalityResult] = useState<typeof personalityTypes[0] | null>(null);
  const [showLoading, setShowLoading] = useState(false);
  // Create fixed gradient colors for each question to avoid type issues
  // Using balanced gradients for better text contrast but still visually appealing
  const questionGradients = [
    ['#000000', '#1A0838', '#2A1060', '#3A1880', '#4A1E9F'] as const, // Purple
    ['#000000', '#1F0A05', '#2D0F08', '#3B140B', '#4D1007'] as const, // Red
    ['#000000', '#051028', '#081A40', '#0B2458', '#0047AB'] as const, // Blue
    ['#000000', '#052820', '#083830', '#0B4840', '#008080'] as const, // Teal
    ['#000000', '#280528', '#380838', '#480B48', '#8A2BE2'] as const, // Magenta
    ['#000000', '#281005', '#381A08', '#48240B', '#D2691E'] as const, // Orange
    ['#000000', '#05280F', '#083816', '#0B481D', '#228B22'] as const, // Green
  ];

  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const slideAnim = useRef(new Animated.Value(0)).current;
  const loadingProgress = useRef(new Animated.Value(0)).current;
  const resultFadeAnim = useRef(new Animated.Value(0)).current;

  // Set empty header title on mount
  useEffect(() => {
    navigation.setOptions({
      headerShown: false,
      title: '',
    });
  }, [navigation]);

  // Handle option selection
  const handleSelectOption = (optionId: string) => {
    // Save the answer
    const newAnswers = { ...answers, [currentQuestionIndex]: optionId };
    setAnswers(newAnswers);

    // Animate transition
    Animated.sequence([
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: -width,
        duration: 0,
        useNativeDriver: true,
      }),
    ]).start(() => {
      // Move to next question or show result
      if (currentQuestionIndex < quizQuestions.length - 1) {
        setCurrentQuestionIndex(currentQuestionIndex + 1);
        // Gradient changes automatically based on currentQuestionIndex
        // Reset animations
        fadeAnim.setValue(0);
        slideAnim.setValue(width);
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(slideAnim, {
            toValue: 0,
            duration: 300,
            useNativeDriver: true,
          }),
        ]).start();
      } else {
        // Show loading screen before results
        setShowLoading(true);

        // Animate loading progress
        Animated.timing(loadingProgress, {
          toValue: 1,
          duration: 3000, // 3 seconds loading animation
          useNativeDriver: false,
        }).start(() => {
          // Determine personality type based on answers
          const personalityType = determinePersonalityType(newAnswers);
          setPersonalityResult(personalityType);

          // Save personality type to AsyncStorage
          AsyncStorage.setItem('personalityType', personalityType.id);

          // Show result screen
          setShowResult(true);

          // Animate result screen
          Animated.timing(resultFadeAnim, {
            toValue: 1,
            duration: 500,
            useNativeDriver: true,
          }).start();
        });
      }

      // Reset animations for next question
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  // Determine personality type based on answers
  const determinePersonalityType = (userAnswers: Record<number, string>) => {
    // Simple algorithm to determine personality type
    // Count occurrences of each option
    const counts: Record<string, number> = { a: 0, b: 0, c: 0, d: 0 };

    Object.values(userAnswers).forEach(answer => {
      if (answer in counts) {
        counts[answer]++;
      }
    });

    // Find the most common answer
    let maxCount = 0;
    let dominantType = 'a';

    Object.entries(counts).forEach(([type, count]) => {
      if (count > maxCount) {
        maxCount = count;
        dominantType = type;
      }
    });

    // Map dominant answer to personality type
    let personalityIndex = 0;

    switch (dominantType) {
      case 'a':
        personalityIndex = counts.d > counts.b ? 0 : 5; // Ghost Magnet or Dry Texter
        break;
      case 'b':
        personalityIndex = counts.c > counts.a ? 1 : 6; // Overthinker or Mirror
        break;
      case 'c':
        personalityIndex = counts.b > counts.d ? 3 : 4; // Hopeful Romantic or Flirt Bot
        break;
      case 'd':
        personalityIndex = counts.a > counts.c ? 2 : 7; // IDGAF or Casual
        break;
      default:
        personalityIndex = 1; // Default to Overthinker
    }

    return personalityTypes[personalityIndex];
  };

  // Handle continue button on result screen
  const handleContinue = () => {
    router.replace('/(auth)/subscription-cta');
  };

  // Handle skip onboarding button
  const handleSkipOnboarding = () => {
    router.replace('/(auth)/subscription-cta');
  };

  // Current question
  const currentQuestion = quizQuestions[currentQuestionIndex];

  // Loading bar width interpolation
  const loadingWidth = loadingProgress.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
  });

  return (
    <View style={styles.container}>
      {/* Add the gradient background */}
      <LinearGradient
        colors={
          showResult ? ['#000000', '#1A0838', '#2A1060', '#3A1880'] :
          showLoading ? questionGradients[0] :
          questionGradients[currentQuestionIndex % questionGradients.length]
        }
        style={styles.background}
        start={{ x: 0.1, y: 0.1 }}
        end={{ x: 0.9, y: 0.9 }}
      />
      <StatusBar barStyle="light-content" />

      {/* Skip Onboarding Button */}
      {!showLoading && !showResult && (
        <TouchableOpacity
          style={styles.skipButton}
          onPress={handleSkipOnboarding}
          activeOpacity={0.7}
        >
          <Text style={styles.skipButtonText}>Skip Onboarding</Text>
        </TouchableOpacity>
      )}

      {!showLoading && !showResult && (
        <Animated.View
          style={[
            styles.questionContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateX: slideAnim }]
            }
          ]}
        >
          {/* Progress indicator */}
          <View style={styles.progressContainer}>
            {quizQuestions.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.progressDot,
                  index === currentQuestionIndex ? styles.progressDotActive : null
                ]}
              />
            ))}
          </View>

          {/* Question */}
          <Text style={styles.questionNumber}>Question {currentQuestionIndex + 1}/{quizQuestions.length}</Text>
          <View style={{ width: '100%', marginBottom: 0 }}>
            <Text style={styles.questionText}>{currentQuestion.question}</Text>
          </View>

          {/* Options - Make sure this is scrollable and has enough height */}
          <ScrollView
            style={styles.optionsContainer}
            contentContainerStyle={styles.optionsContentContainer}
            showsVerticalScrollIndicator={false}
          >
            {currentQuestion.options.map((option) => (
              <TouchableOpacity
                key={option.id}
                style={styles.optionCard}
                onPress={() => handleSelectOption(option.id)}
                activeOpacity={0.8}
              >
                <RNText style={styles.optionIcon}>{option.icon}</RNText>
                <Text style={styles.optionText}>{option.text}</Text>
              </TouchableOpacity>
            ))}
            {/* Add extra padding at the bottom to ensure all options are visible */}
            <View style={styles.optionsPadding} />
          </ScrollView>
        </Animated.View>
      )}

      {showLoading && !showResult && (
        <View style={styles.loadingContainer}>
          <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 20 }}>
            <RNText style={styles.optionIcon}>🔍</RNText>
            <RNText style={{
              fontSize: 20,
              color: '#FFFFFF',
              fontWeight: '600',
              textAlign: 'center',
              fontFamily: satoshiRegular
            }}>Analyzing your texting DNA...</RNText>
          </View>
          <View style={styles.loadingBarContainer}>
            <Animated.View
              style={[
                styles.loadingBar,
                { width: loadingWidth }
              ]}
            />
          </View>
        </View>
      )}

      {showResult && personalityResult && (
        <Animated.View style={[styles.resultContainer, { opacity: resultFadeAnim }]}>
          <RNText style={{
            fontSize: 26,
            fontWeight: 'bold',
            color: '#FFFFFF',
            marginBottom: 25,
            textAlign: 'center',
            width: '90%',
            fontFamily: satoshiRegular
          }}>Your Texting Personality</RNText>

          <View style={[styles.personalityCard, {
            backgroundColor: personalityResult.color,
            width: '90%',
            padding: 25,
            alignItems: 'center',
            marginBottom: 30,
            borderRadius: 20
          }]}>
            <RNText style={{
              fontSize: 24,
              fontWeight: 'bold',
              color: '#FFFFFF',
              marginBottom: 15,
              textAlign: 'center',
              fontFamily: satoshiRegular
            }}>{personalityResult.title}</RNText>
            <RNText style={{
              fontSize: 16,
              color: '#FFFFFF',
              textAlign: 'center',
              lineHeight: 24,
              paddingHorizontal: 5,
              fontFamily: satoshiRegular
            }}>{personalityResult.description}</RNText>
          </View>

          <TouchableOpacity
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: '#00F2EA',
              paddingVertical: 15,
              paddingHorizontal: 30,
              borderRadius: 20,
              width: '90%'
            }}
            onPress={handleContinue}
            activeOpacity={0.8}
          >
            <RNText style={{
              color: '#000000',
              fontSize: 18,
              fontWeight: 'bold',
              marginRight: 10,
              textAlign: 'center',
              fontFamily: satoshiRegular
            }}>Unlock Premium Features</RNText>
            <MaterialIcons name="arrow-forward" size={24} color="#000" />
          </TouchableOpacity>
        </Animated.View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  // Background style is defined below
  container: {
    flex: 1,
    position: 'relative',
    // Ensure content is above the gradient
    backgroundColor: 'transparent', // Make container transparent
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: -1, // Ensure it's behind other content
  },
  questionContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'flex-start', // Align to top
    paddingHorizontal: 20,
    paddingTop: (StatusBar.currentHeight || 0) + 150, // Increased top padding
    paddingBottom: 0, // Removed bottom padding
  },
  progressContainer: {
    flexDirection: 'row',
    marginBottom: 30,
    position: 'absolute',
    top: (StatusBar.currentHeight || 0) + 100, // Moved down further
    alignSelf: 'center', // Center horizontally
  },
  progressDot: {
    width: 10, // Slightly larger
    height: 10, // Slightly larger
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.3)', // More visible on dark background
    marginHorizontal: 6, // More spacing between dots
  },
  progressDotActive: {
    backgroundColor: '#00F2EA',
    width: 14, // Slightly larger
    height: 14, // Slightly larger
    borderRadius: 7,
  },
  questionNumber: {
    color: '#00F2EA', // Cyan color for accent
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5, // Reduced margin
    marginTop: 0,
  },
  questionText: {
    fontSize: 24, // Further increased font size
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 10, // Reduced bottom margin
    paddingHorizontal: 20, // Reduced horizontal padding
    paddingVertical: 10, // Reduced vertical padding
    lineHeight: 32, // Added line height for better spacing between text lines
    textShadowColor: 'rgba(0, 0, 0, 0.5)', // Added shadow for better visibility
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  optionsContainer: {
    width: '100%',
    maxHeight: height * 0.6, // Increased max height
    flexGrow: 0, // Prevent ScrollView from expanding beyond its content
    marginTop: 10, // Reduced margin to remove the gap
    backgroundColor: 'transparent', // Ensure transparent background
  },
  // Add content container style for ScrollView
  optionsContentContainer: {
    paddingBottom: 30, // Increased padding at the bottom
    paddingTop: 0, // Removed top padding
    paddingHorizontal: 20, // Increased horizontal padding for better alignment
    width: '100%', // Ensure full width
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: '#2a2a2a', // Use theme color
    paddingVertical: 18, // Increased padding
    paddingHorizontal: 20,
    borderRadius: 15, // More rounded
    marginBottom: 15, // Increased spacing
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)', // Subtle border
  },
  // optionIcon style moved below
  optionCard: { // Renamed from optionButton for clarity
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start', // Ensure content starts from the left
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Match personality card opacity
    paddingVertical: 15, // Reduced vertical padding
    paddingHorizontal: 20, // Horizontal padding
    borderRadius: 20, // Match personality card
    marginBottom: 15,
    marginTop: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 }, // Match personality card
    shadowOpacity: 0.3, // Match personality card
    shadowRadius: 10, // Match personality card
    elevation: 10, // Match personality card
    minHeight: 70, // Increased minimum height to show icons better
    width: '100%', // Ensure full width
  },
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.4)', // Lighter background for better emoji visibility
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)', // Brighter border
  },
  optionIcon: {
    fontSize: 32, // Increased size
    textAlign: 'center',
    opacity: 1,
    marginRight: 15,
    width: 40, // Fixed width for consistent spacing
    // Removed color, fontFamily and includeFontPadding to allow system emoji rendering
  },
  optionText: {
    fontSize: 18, // Increased font size
    color: '#FFFFFF', // Full white for better visibility
    flex: 1,
    fontWeight: '600', // Increased font weight
    lineHeight: 24, // Match personality description
    paddingRight: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)', // Light shadow for better contrast
    textShadowOffset: { width: 0.5, height: 0.5 },
    textShadowRadius: 1,
  },
  // Add padding at the bottom of options
  optionsPadding: {
    height: 40, // Increased padding
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20, // Reduced horizontal padding
    paddingVertical: 20, // Added vertical padding
    backgroundColor: 'transparent', // Ensure transparent background to show gradient
  },
  loadingText: {
    fontSize: 18, // Reduced to match answer choices
    fontWeight: '600',
    color: '#FFFFFF', // Explicitly set color to white
    marginBottom: 25,
    textAlign: 'center',
    paddingHorizontal: 20, // Added horizontal padding
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  loadingBarContainer: {
    width: '80%',
    height: 12, // Increased height
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 6, // Increased border radius
    overflow: 'hidden',
    marginTop: 30, // Increased margin top
    marginBottom: 20, // Added margin bottom
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  loadingBar: {
    height: '100%',
    backgroundColor: '#00F2EA', // Use a theme color or accent
    borderRadius: 6, // Increased border radius
    shadowColor: '#00F2EA',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 8,
  },
  // progressBarBackground defined above
  // progressBar defined above
  resultContainer: {
    flex: 1,
    justifyContent: 'center', // Center vertically
    alignItems: 'center', // Center horizontally
    paddingHorizontal: 20,
    paddingVertical: 20,
    opacity: 0, // Initial state for animation
    backgroundColor: 'transparent', // Ensure transparent background to show gradient
  },
  resultCard: {
    width: '100%',
    borderRadius: 20,
    padding: 30,
    alignItems: 'center',
    marginBottom: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
  },
  resultEmoji: {
    fontSize: 60,
    marginBottom: 15,
  },
  resultTitle: {
    fontSize: 26, // Increased size
    fontWeight: 'bold',
    color: '#FFFFFF', // Explicitly set color
    marginBottom: 25, // Increased spacing
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  personalityCard: {
    width: '90%',
    borderRadius: 20,
    padding: 25,
    alignItems: 'center',
    marginBottom: 30, // Increased bottom margin
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 5 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
  },
  personalityTitle: {
    fontSize: 24, // Increased size
    fontWeight: 'bold',
    color: '#FFFFFF', // Keep white for contrast on colored card
    marginBottom: 15, // Increased spacing
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  personalityDescription: {
    fontSize: 16,
    color: '#FFFFFF', // Full white for better visibility
    textAlign: 'center',
    lineHeight: 24,
    paddingHorizontal: 5, // Added horizontal padding
  },
  resultDescription: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
  },
  continueButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#00F2EA', // Use accent color
    paddingVertical: 15,
    paddingHorizontal: 30,
    borderRadius: 20,
    shadowColor: '#00F2EA',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 8,
    elevation: 8,
    width: '90%',
  },
  continueButtonText: {
    color: '#000000', // Changed to black for better contrast on cyan
    fontSize: 18,
    fontWeight: 'bold',
    marginRight: 10,
    textAlign: 'center',
  },
  skipButton: {
    position: 'absolute',
    bottom: 80, // Increased from 20 to 80 to position it higher
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    zIndex: 10,
  },
  skipButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: satoshiRegular,
    lineHeight: 20,
  },
});
