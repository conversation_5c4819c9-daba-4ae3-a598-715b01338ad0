'use strict';

const WebSocket = require('./lib/websocket');

// React Native polyfill: WebSocket streams are not supported
WebSocket.createWebSocketStream = function() {
  console.warn('WebSocket streams are not supported in React Native');
  return {
    on: function() {},
    emit: function() {},
    write: function() {},
    end: function() {},
    destroy: function() {},
    pipe: function() { return this; },
    unpipe: function() { return this; },
  };
};

WebSocket.Server = require('./lib/websocket-server');
WebSocket.Receiver = require('./lib/receiver');
WebSocket.Sender = require('./lib/sender');

WebSocket.WebSocket = WebSocket;
WebSocket.WebSocketServer = WebSocket.Server;

module.exports = WebSocket;
