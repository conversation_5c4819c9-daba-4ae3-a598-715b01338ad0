import { useEffect, useState } from 'react';
import * as Font from 'expo-font';

/**
 * Custom hook to load fonts
 * @returns {boolean} Whether fonts are loaded
 */
export function useFonts() {
  const [fontsLoaded, setFontsLoaded] = useState(false);

  useEffect(() => {
    // Font loading is handled in _layout.tsx via loadFonts
    // This hook now primarily signals that the font loading process
    // has been initiated elsewhere.
    // We assume fonts will be loaded by the time they are needed.
    setFontsLoaded(true);
  }, []);

  return fontsLoaded;
}
