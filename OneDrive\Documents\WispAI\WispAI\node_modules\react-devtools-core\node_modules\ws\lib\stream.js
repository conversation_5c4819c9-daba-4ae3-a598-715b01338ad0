// React Native polyfill for ws/lib/stream.js
// This file replaces the original stream.js to prevent Node.js module import errors

function createWebSocketStream() {
  console.warn('WebSocket streams are not supported in React Native');
  return {
    on: function() {},
    emit: function() {},
    write: function() {},
    end: function() {},
    destroy: function() {},
    pipe: function() { return this; },
    unpipe: function() { return this; },
  };
}

// Export the function directly (this is what ws/index.js expects)
module.exports = createWebSocketStream;
