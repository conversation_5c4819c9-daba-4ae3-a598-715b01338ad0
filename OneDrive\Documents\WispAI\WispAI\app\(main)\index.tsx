import React from 'react';
import { View, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import GradientBackground from '../../components/common/GradientBackground';
import GradientCard from '../../components/common/GradientCard';
import ModernTechCard from '../../components/common/ModernTechCard';
import { StatusBar } from 'expo-status-bar';
import { useRouter } from 'expo-router';
import { useNotifications } from '../../contexts/NotificationContext';
import { useTheme } from '../../contexts/ThemeContext';
import Text from '../../components/common/Text';

// Define the Satoshi Regular font family name
const satoshiRegular = 'Satoshi-Regular';

// Safe type for material icons
type MaterialIconName =
  | 'auto-fix-high'
  | 'add'
  | 'history'
  | 'notifications'
  | 'psychology'
  | 'message'
  | 'analytics';

interface TipCardProps {
  title: string;
  description: string;
  icon: MaterialIconName;
}

export default function HomeScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const { notificationsEnabled, toggleNotifications } = useNotifications();

  const handleStartAnalysis = () => {
    // Navigate to the analysis tab
    router.push('/(main)/analysis');
  };

  return (
    <GradientBackground
      style={styles.container}
      colors={['#0A0A0A', '#040114']} // Dark black to very dark purple gradient (subtle purple)
      start={{ x: 0, y: 0.2 }}
      end={{ x: 0, y: 1 }}
    >
      <StatusBar style="light" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>WispAI</Text>
        <TouchableOpacity style={styles.headerButton} onPress={toggleNotifications}>
          <MaterialIcons
            name={notificationsEnabled ? "notifications" : "notifications-off"}
            size={24}
            color={notificationsEnabled ? "#00E5FF" : "#666666"}
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer}>
        {/* Welcome card */}
        <GradientCard
          style={styles.welcomeCard}
          colors={[theme.colors.cardGradient.start, theme.colors.cardGradient.end]}
          borderColor="rgba(0, 229, 255, 0.3)"
          borderRadius={16}
        >
          <MaterialIcons name="auto-fix-high" size={28} color="#00E5FF" />
          <Text style={styles.welcomeTitle}>Welcome to WispAI</Text>
          <Text style={styles.welcomeText}>
            Start decoding your conversations and get insights into your messages.
          </Text>
        </GradientCard>

        {/* Action button */}
        <View style={styles.actionContainer}>
          <TouchableOpacity style={styles.fullWidthActionButton} onPress={handleStartAnalysis}>
            <LinearGradient
              colors={['#00E5FF', '#6AFFC9']}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 0 }}
              style={styles.fullWidthActionButtonGradient}
            >
              <MaterialIcons name="analytics" size={24} color="#000000" />
              <Text style={styles.actionButtonText}>Start Analysis</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Recent analyses section removed */}

        {/* Tips section */}
        <View style={styles.tipsContainer}>
          <Text style={styles.sectionTitle}>Tips & Tricks</Text>

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.tipsScrollContent}
          >
            <ModernTechCard
              title="Understanding Sentiment"
              description="Learn how AI detects emotions in text messages and analyzes conversation patterns"
              icon="psychology"
              onPress={() => {
                // Navigate to help or tutorial section
                router.push('/(main)/settings/help');
              }}
            />
            <TipCard
              title="Conversation Flow"
              description="Tips for maintaining engaging conversations"
              icon="message"
            />
            <TipCard
              title="Reading Between Lines"
              description="How to interpret subtle cues in messages"
              icon="psychology"
            />
          </ScrollView>
        </View>
      </ScrollView>
    </GradientBackground>
  );
}

function TipCard({ title, description, icon }: TipCardProps) {
  const { theme } = useTheme();
  return (
    <GradientCard
      style={styles.tipCard}
      colors={[theme.colors.cardGradient.start, theme.colors.cardGradient.end]}
      borderColor="rgba(0, 229, 255, 0.2)"
      borderRadius={12}
    >
      <View style={styles.tipIconContainer}>
        <MaterialIcons name={icon} size={24} color="#00E5FF" />
      </View>
      <Text style={styles.tipTitle}>{title}</Text>
      <Text style={styles.tipDescription}>{description}</Text>
    </GradientCard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#1A1A1A', // Dark gray (30% - Secondary Accent)
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    fontFamily: satoshiRegular,
    lineHeight: 36, // Added line height to prevent clipping
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#1A1A1A', // Dark gray (30% - Secondary Accent)
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(0, 229, 255, 0.3)', // Cyan border
  },
  scrollContainer: {
    flex: 1,
  },
  welcomeCard: {
    margin: 16,
    padding: 20,
    alignItems: 'center',
  },
  welcomeTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 12,
    marginBottom: 8,
    fontFamily: satoshiRegular,
    lineHeight: 28, // Added line height to prevent clipping
  },
  welcomeText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 20,
    fontFamily: satoshiRegular,
  },
  actionContainer: {
    padding: 16,
  },
  fullWidthActionButton: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  fullWidthActionButtonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  actionButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginLeft: 8,
    fontFamily: satoshiRegular,
    lineHeight: 26, // Added line height to prevent clipping
  },
  recentContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 12,
    fontFamily: satoshiRegular,
    lineHeight: 26, // Added line height to prevent clipping
  },
  emptyStateText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    padding: 20,
    fontFamily: satoshiRegular,
    lineHeight: 20, // Added line height to prevent clipping
  },
  tipsContainer: {
    padding: 16,
    marginBottom: 24,
    borderTopWidth: 1,
    borderTopColor: '#1A1A1A', // Dark gray (30% - Secondary Accent)
    paddingTop: 24,
    marginTop: 8,
  },
  tipsScrollContent: {
    paddingRight: 16,
    paddingLeft: 4, // Add some left padding for better spacing
  },
  tipCard: {
    width: 220,
    padding: 16,
    marginRight: 12,
  },
  tipIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 229, 255, 0.1)', // Cyan background
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
    borderWidth: 1,
    borderColor: 'rgba(0, 229, 255, 0.3)', // Cyan border
  },
  tipTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    fontFamily: satoshiRegular,
    lineHeight: 24, // Added line height to prevent clipping
  },
  tipDescription: {
    fontSize: 13,
    color: '#FFFFFF',
    lineHeight: 18,
    fontFamily: satoshiRegular,
  },
});