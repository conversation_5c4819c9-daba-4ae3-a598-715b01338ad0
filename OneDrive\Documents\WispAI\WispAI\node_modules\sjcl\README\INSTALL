SJCL comes with a file sjcl.js pre-built.  This default build includes
all the modules except for sjcl.codec.bytes (because the demo site doesn't
use it).  All you need to do to install is copy this file to your web
server and start using it.

SJCL is divided into modules implementing various cryptographic and
convenience functions.  If you don't need them all for your application,
you can reconfigure SJ<PERSON> for a smaller code size.  To do this, you can
run

./configure --without-all --with-aes --with-sha256 ...

Then type

make

to rebuild sjcl.js.  This will also create a few intermediate files
core*.js; you can delete these automatically by typing

make sjcl.js tidy

instead.  You will need make, perl, bash and java to rebuild SJCL.

Some of the modules depend on other modules; configure should handle this
automatically unless you tell it --without-FOO --with-BAR, where BAR
depends on FOO.  If you do this, configure will yell at you.

SJCL is compressed by stripping comments, shortening variable names, etc.
You can also pass a --compress argument to configure to change the
compressor.  By default SJCL uses some perl/sh scripts and Google's
Closure compressor.

If you reconfigure SJCL, it is recommended that you run the included test
suite by typing "make test".  If this prints "FAIL" or segfaults, SJCL
doesn't work; please file a bug.

