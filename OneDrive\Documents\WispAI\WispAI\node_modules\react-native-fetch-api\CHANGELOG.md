# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [3.0.0](https://github.com/react-native-community/fetch/compare/v2.0.0...v3.0.0) (2021-08-02)


### ⚠ BREAKING CHANGES

* Response returns the instance rather than a promise in all cases

### Bug Fixes

* response correctly returns instance instead of promise ([8b6c3d3](https://github.com/react-native-community/fetch/commit/8b6c3d3ee97ba142c2bd1d341e2c072bac6262f8))

## [2.0.0](https://github.com/react-native-community/fetch/compare/v1.0.2...v2.0.0) (2021-06-28)

### [1.0.2](https://github.com/react-native-community/fetch/compare/v1.0.1...v1.0.2) (2021-01-31)


### Bug Fixes

* do not abort native request if none was made yet ([3cc8669](https://github.com/react-native-community/fetch/commit/3cc8669c9ed016e115e820a959b3015ed7bdab0d))
* implement Response.body for each input body type ([0eec6f4](https://github.com/react-native-community/fetch/commit/0eec6f49e2f01c9e528db3c6318406d9cc2fa833))

### [1.0.1](https://github.com/react-native-community/fetch/compare/v1.0.0...v1.0.1) (2021-01-25)


### Bug Fixes

* handle blobs as stream when FileReader.readAsArrayBuffer is available ([6ebb08a](https://github.com/react-native-community/fetch/commit/6ebb08a9093888a8c3f1839cdec22b4728a0b033))

## 1.0.0 (2021-01-25)
