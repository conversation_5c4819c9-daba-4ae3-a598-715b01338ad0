// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Find the project and workspace directories
const projectRoot = __dirname;
// This can be removed if not using a monorepo
const workspaceRoot = projectRoot;

const config = getDefaultConfig(projectRoot);

// 1. Watch all files in the project directory
config.watchFolders = [workspaceRoot];

// 2. Let Metro know where to resolve packages
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(workspaceRoot, 'node_modules'),
];

// 3. Force Metro to resolve (sub)dependencies only from the root node_modules
config.resolver.disableHierarchicalLookup = true;

// Set the Expo Router app root directory explicitly without using process.env
// This ensures the right folder is used
config.resolver.sourceExts = [...config.resolver.sourceExts, 'mjs'];

// 4. Add resolver blockList to completely exclude ws package
config.resolver.blockList = [
  // Block all ws package files
  /.*\/node_modules\/ws\/.*$/,
  /.*\/node_modules\/.*\/ws\/.*$/,
  // Block specific problematic files
  /.*\/node_modules\/.*\/ws\/lib\/stream\.js$/,
  /.*\/node_modules\/ws\/lib\/stream\.js$/,
  /.*\/node_modules\/ws\/lib\/.*$/,
  // Block any file that tries to import ws
  /.*ws.*stream.*$/,
];

// 5. Add resolver alias to handle Node.js modules that don't work in React Native
config.resolver.alias = {
  ...config.resolver.alias,
  // Map Node.js modules to React Native compatible alternatives or disable them
  'stream': 'stream-browserify',
  'crypto': path.resolve(projectRoot, 'polyfills.js'),
  'ws': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/stream': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/websocket': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/constants': path.resolve(projectRoot, 'polyfills.js'),
  // Fix web-streams-polyfill issue
  'web-streams-polyfill/ponyfill/es6': 'web-streams-polyfill/ponyfill',
  'http': false,
  'https': false,
  'net': false,
  'tls': false,
  'fs': false,
  'path': false,
  'os': false,
  'util': false,
  'events': false,
  'buffer': false,
  'url': false,
  'querystring': false,
};

// 6. Add resolver platforms to ensure proper resolution
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;