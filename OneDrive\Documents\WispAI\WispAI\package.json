{"name": "wispai", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "build:android": "eas build --platform android --profile production", "build:ios": "eas build --platform ios --profile production", "build:preview": "eas build --profile preview", "prebuild": "expo prebuild", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@react-native-async-storage/async-storage": "2.1.2", "@react-native-clipboard/clipboard": "^1.16.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/netinfo": "^11.4.1", "@supabase/supabase-js": "^2.49.1", "axios": "^1.8.4", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "expo": "^53.0.9", "expo-blur": "~14.1.4", "expo-clipboard": "~7.1.4", "expo-constants": "~17.1.6", "expo-document-picker": "~13.1.5", "expo-image": "~2.1.7", "expo-image-manipulator": "~13.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "express": "^4.21.2", "openai": "^4.89.0", "pdf-lib": "^1.17.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-crypto": "^2.2.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.13.1", "react-native-polyfill-globals": "^3.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-stream": "^0.1.9", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "sharp": "^0.33.5", "tesseract.js": "^6.0.0", "uuid": "^11.1.0", "victory-native": "^36.6.8"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.3", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.26.1", "@typescript-eslint/parser": "^8.26.1", "babel-plugin-inline-dotenv": "^1.7.0", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^9.22.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "jest": "^29.7.0", "jest-expo": "~53.0.5", "prettier": "^3.5.3", "react-native-dotenv": "^3.4.11", "react-test-renderer": "19.0.0", "typescript": "^5.3.3"}, "private": true}