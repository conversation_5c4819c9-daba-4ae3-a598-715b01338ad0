/**
 * Notification Context Provider
 *
 * This file provides a React context for managing the application's notification settings.
 * It handles notification permissions and state across the app.
 *
 * Key features:
 * - Notification permission management
 * - Notification toggle functionality
 * - Notification state tracking
 * - Custom hook (useNotifications) for accessing notification state
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import * as Notifications from 'expo-notifications';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert, Linking } from 'react-native';
import { registerForPushNotifications } from '../lib/notifications';

interface NotificationContextType {
  notificationsEnabled: boolean;
  toggleNotifications: () => Promise<void>;
  requestNotificationPermission: () => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(false);

  // Check notification permission status on component mount
  useEffect(() => {
    const checkNotificationStatus = async () => {
      try {
        // Check current permission status
        const { status } = await Notifications.getPermissionsAsync();
        setNotificationsEnabled(status === 'granted');

        // Check if we've already asked for permission
        const hasAskedForPermission = await AsyncStorage.getItem('hasAskedForNotificationPermission');

        // If we haven't asked before, request permission with our custom dialog
        if (hasAskedForPermission !== 'true') {
          // Wait a moment before showing the permission dialog
          // This gives the app time to fully load
          setTimeout(() => {
            requestNotificationPermission();
          }, 1000);
        }
      } catch (error) {
        console.error('Error checking notification status:', error);
      }
    };

    checkNotificationStatus();
  }, []);

  // Request notification permission with custom dialog
  const requestNotificationPermission = async () => {
    try {
      // First, check if we've already asked for permission
      const { status: existingStatus } = await Notifications.getPermissionsAsync();

      // If permission is already granted, just update state
      if (existingStatus === 'granted') {
        setNotificationsEnabled(true);
        const token = await registerForPushNotifications();
        console.log('Expo push token:', token);
        return;
      }

      // If we haven't asked yet, show our custom dialog first
      if (existingStatus !== 'denied') {
        // Show custom dialog
        Alert.alert(
          'Enable Notifications',
          'WispAI would like to send you notifications for important updates and messages.',
          [
            {
              text: 'Not Now',
              style: 'cancel',
              onPress: async () => {
                // Save that we've asked for permission
                await AsyncStorage.setItem('hasAskedForNotificationPermission', 'true');
                setNotificationsEnabled(false);
              }
            },
            {
              text: 'Allow',
              onPress: async () => {
                // Now request system permission
                const { status } = await Notifications.requestPermissionsAsync();
                setNotificationsEnabled(status === 'granted');

                // Save that we've asked for permission
                await AsyncStorage.setItem('hasAskedForNotificationPermission', 'true');

                // If permission granted, register for push notifications
                if (status === 'granted') {
                  const token = await registerForPushNotifications();
                  console.log('Expo push token:', token);
                }
              }
            }
          ]
        );
      } else {
        // If permission was previously denied, directly open settings
        // without showing an alert
        Linking.openSettings();
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  };

  // Toggle notifications without alerts
  const toggleNotifications = async () => {
    if (notificationsEnabled) {
      // If notifications are enabled, directly toggle them off
      // We'll just open settings since we can't programmatically disable notifications
      Linking.openSettings();
    } else {
      // If notifications are disabled, request permission
      requestNotificationPermission();
    }
  };

  return (
    <NotificationContext.Provider value={{
      notificationsEnabled,
      toggleNotifications,
      requestNotificationPermission
    }}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
