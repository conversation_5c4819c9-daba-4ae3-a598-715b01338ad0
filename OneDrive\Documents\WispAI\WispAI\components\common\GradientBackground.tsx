import React from 'react';
import { StyleSheet, View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../contexts/ThemeContext';

interface GradientBackgroundProps {
  children: React.ReactNode;
  style?: any;
  colors?: string[];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
}

/**
 * A component that provides a gradient background using the theme colors
 */
export default function GradientBackground({
  children,
  style,
  colors,
  start = { x: 0, y: 0 },
  end = { x: 0, y: 1 },
}: GradientBackgroundProps) {
  const { theme } = useTheme();

  // Default to the theme's background gradient colors
  const gradientColors = colors || [
    theme.colors.backgroundGradient.start,
    theme.colors.backgroundGradient.end,
  ];

  return (
    <LinearGradient
      colors={gradientColors}
      style={[styles.container, style]}
      start={start}
      end={end}
    >
      {children}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
