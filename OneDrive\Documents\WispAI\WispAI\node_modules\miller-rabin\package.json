{"name": "miller-rabin", "version": "4.0.1", "description": "<PERSON> algorithm for primality test", "main": "lib/mr.js", "bin": "bin/miller-rabin", "scripts": {"test": "mocha --reporter=spec test/**/*-test.js"}, "repository": {"type": "git", "url": "**************:indutny/miller-rabin"}, "keywords": ["prime", "miller-rabin", "bignumber"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/indutny/miller-rabin/issues"}, "homepage": "https://github.com/indutny/miller-rabin", "devDependencies": {"mocha": "^2.0.1"}, "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}}