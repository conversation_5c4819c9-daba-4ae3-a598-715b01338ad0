{"author": "<PERSON> <<EMAIL>> (dominictarr.com)", "name": "react-native-crypto", "description": "implementation of crypto for React Native", "version": "2.2.0", "homepage": "https://github.com/mvayngrib/react-native-crypto", "repository": {"type": "git", "url": "git://github.com/mvayngrib/react-native-crypto.git"}, "scripts": {"standard": "standard", "test": "npm run standard && npm run unit", "unit": "Echo 'tests not set up for React Native yet'"}, "engines": {"node": "*"}, "dependencies": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.4", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "3.0.8", "public-encrypt": "^4.0.0", "randomfill": "^1.0.3"}, "peerDependencies": {"react-native-randombytes": ">=2.0.0 <4.0.0"}, "devDependencies": {"hash-test-vectors": "~1.3.2", "pseudorandombytes": "^2.0.0", "standard": "^5.0.2", "tape": "~2.3.2", "zuul": "^3.6.0"}, "optionalDependencies": {}, "keywords": ["react-native", "react-component", "ios"], "license": "MIT"}