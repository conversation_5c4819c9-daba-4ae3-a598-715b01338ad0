# Changelog

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

## [3.1.0](https://github.com/acostalima/react-native-polyfill-globals/compare/v3.0.0...v3.1.0) (2021-02-01)


### Features

* add polyfill for crypto.getRandomValues ([3890804](https://github.com/acostalima/react-native-polyfill-globals/commit/3890804fbf638bb213c9898187a2ebdcea6c60ad))

## [3.0.0](https://github.com/acostalima/react-native-polyfill-globals/compare/v2.0.0...v3.0.0) (2021-01-25)


### ⚠ BREAKING CHANGES

* update fetch package name

### Bug Fixes

* update fetch package name ([f0daf44](https://github.com/acostalima/react-native-polyfill-globals/commit/f0daf445ba6fd5ed352bb1f25636d005cfb06c16))

## [2.0.0](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.7...v2.0.0) (2021-01-15)


### ⚠ BREAKING CHANGES

* add @react-native-community/fetch, update docs, add polyfills as peer deps

### Features

* add @react-native-community/fetch, update docs, add polyfills as peer deps ([644fd6d](https://github.com/acostalima/react-native-polyfill-globals/commit/644fd6dcfafab5a1e8d000c702e26844827be7b3))

### [1.0.7](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.6...v1.0.7) (2020-11-12)


### Bug Fixes

* bump web-streams-polyfill version ([fd64c94](https://github.com/acostalima/react-native-polyfill-globals/commit/fd64c94370d8b83274a204835d1fb4a2132dd230))

### [1.0.6](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.5...v1.0.6) (2020-10-23)

### [1.0.5](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.4...v1.0.5) (2020-10-22)


### Bug Fixes

* use ReadableStream ES6 ponyfill ([0541c80](https://github.com/acostalima/react-native-polyfill-globals/commit/0541c807d274d9fd3e78e273c93b0b23020ba2e4))

### [1.0.4](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.3...v1.0.4) (2020-10-22)


### Bug Fixes

* switch to named exports ([14586da](https://github.com/acostalima/react-native-polyfill-globals/commit/14586daa460ccbee42d777323bcfb3410d232aca))

### [1.0.3](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.2...v1.0.3) (2020-10-22)


### Bug Fixes

* polyfill all function; added auto polyfill ([57c4c28](https://github.com/acostalima/react-native-polyfill-globals/commit/57c4c2804c2a270b321c93dc23c2ff66532b4d6a))

### [1.0.2](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.1...v1.0.2) (2020-10-22)


### Bug Fixes

* make blob() async and enqueue Uint8Array to stream controller ([7a59f51](https://github.com/acostalima/react-native-polyfill-globals/commit/7a59f519e08c74c4c2576b869c6923bd5f0df94d))

### [1.0.1](https://github.com/acostalima/react-native-polyfill-globals/compare/v1.0.0...v1.0.1) (2020-10-22)


### Bug Fixes

* add patch for whatwg-fetch ([76a77b3](https://github.com/acostalima/react-native-polyfill-globals/commit/76a77b38f231abe23443897ed7cfe7668f215c8a))

## 1.0.0 (2020-10-22)
