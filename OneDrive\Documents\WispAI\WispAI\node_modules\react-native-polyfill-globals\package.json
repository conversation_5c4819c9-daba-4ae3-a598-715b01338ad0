{"name": "react-native-polyfill-globals", "version": "3.1.0", "description": "Polyfills and patches missing or partially supported web and core APIs", "main": "index.js", "repository": "acostalima/react-native-polyfill-globals", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/acostalima/"}, "license": "MIT", "files": ["src", "index.js", "auto.js", "patches"], "keywords": ["react-native", "polyfill", "polyfills", "patch", "patches"], "scripts": {"lint": "eslint --cache --ignore-path .gitignore .", "prerelease": "npm run lint && npm run test", "release": "standard-version", "postrelease": "git push --follow-tags origin HEAD && conventional-github-releaser -p angular", "test": "jest"}, "devDependencies": {"@babel/core": "^7.12.3", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@moxy/babel-preset": "^3.3.1", "@moxy/eslint-config-babel": "^12.3.0", "@moxy/eslint-config-base": "^12.2.0", "@moxy/eslint-config-jest": "^12.3.0", "@moxy/eslint-config-react-native": "^13.0.3", "@moxy/jest-config-base": "^5.0.0", "@moxy/jest-config-react-native": "^5.0.0", "conventional-github-releaser": "^3.1.5", "eslint": "^6.8.0", "husky": "^4.3.0", "jest": "^26.6.0", "jest-extended": "^0.11.5", "lint-staged": "^10.4.2", "metro-react-native-babel-preset": "^0.63.0", "patch-package": "^6.2.2", "react-native": "^0.63.2", "standard-version": "^9.0.0"}, "peerDependencies": {"base-64": "*", "react-native-url-polyfill": "*", "text-encoding": "*", "web-streams-polyfill": "*", "react-native-fetch-api": "*", "react-native-get-random-values": "*"}}