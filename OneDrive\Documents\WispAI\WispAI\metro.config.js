// Learn more https://docs.expo.io/guides/customizing-metro
const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

// Find the project and workspace directories
const projectRoot = __dirname;
// This can be removed if not using a monorepo
const workspaceRoot = projectRoot;

const config = getDefaultConfig(projectRoot);

// 1. Watch all files in the project directory
config.watchFolders = [workspaceRoot];

// 2. Let Metro know where to resolve packages
config.resolver.nodeModulesPaths = [
  path.resolve(projectRoot, 'node_modules'),
  path.resolve(workspaceRoot, 'node_modules'),
];

// 3. Force Metro to resolve (sub)dependencies only from the root node_modules
config.resolver.disableHierarchicalLookup = true;

// Set the Expo Router app root directory explicitly without using process.env
// This ensures the right folder is used
config.resolver.sourceExts = [...config.resolver.sourceExts, 'mjs'];

// 4. Add resolver blockList to completely exclude problematic ws files
config.resolver.blockList = [
  // Block specific problematic ws files that use Node.js modules
  /.*\/node_modules\/.*\/ws\/lib\/receiver\.js$/,
  /.*\/node_modules\/ws\/lib\/receiver\.js$/,
  /.*\/node_modules\/.*\/ws\/lib\/sender\.js$/,
  /.*\/node_modules\/ws\/lib\/sender\.js$/,
  /.*\/node_modules\/.*\/ws\/lib\/websocket-server\.js$/,
  /.*\/node_modules\/ws\/lib\/websocket-server\.js$/,
];

// 5. Add resolver alias to handle Node.js modules that don't work in React Native
config.resolver.alias = {
  ...config.resolver.alias,
  // Map Node.js modules to React Native compatible alternatives or disable them
  'stream': path.resolve(projectRoot, 'polyfills.js'),
  'crypto': path.resolve(projectRoot, 'polyfills.js'),
  'ws': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/stream': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/websocket': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/constants': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/receiver': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/sender': path.resolve(projectRoot, 'polyfills.js'),
  'ws/lib/websocket-server': path.resolve(projectRoot, 'polyfills.js'),
  // Fix web-streams-polyfill issue
  'web-streams-polyfill/ponyfill/es6': 'web-streams-polyfill/ponyfill',
  'http': false,
  'https': false,
  'net': false,
  'tls': false,
  'fs': false,
  'path': false,
  'os': false,
  'util': false,
  'events': false,
  'buffer': false,
  'url': false,
  'querystring': false,
};

// 6. Add resolver platforms to ensure proper resolution
config.resolver.platforms = ['ios', 'android', 'native', 'web'];

module.exports = config;