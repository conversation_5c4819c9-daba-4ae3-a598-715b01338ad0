{"expo": {"sdkVersion": "53.0.0", "scheme": "wispai", "plugins": ["expo-router", []], "name": "WispAI", "slug": "wispai", "platforms": {"babel": {"reanimated": false}}, "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "dark", "splash": {"image": "./assets/splash.png", "resizeMode": "contain", "backgroundColor": "#000000"}, "assetBundlePatterns": ["**/*"], "fonts": ["./assets/fonts/Satoshi-Regular.otf"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.wispai.app"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.wispai.app"}, "web": {"favicon": "./assets/favicon.png", "bundler": "metro"}, "extra": {"eas": {"projectId": "your-project-id-here"}}, "notification": {"icon": "./assets/notification-icon.png", "color": "#00F2EA", "androidMode": "default", "androidCollapsedTitle": "WispAI", "iosDisplayInForeground": true}}}