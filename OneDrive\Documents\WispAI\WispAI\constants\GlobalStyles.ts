import { StyleSheet, Platform, TextStyle } from 'react-native';
import { FontFamily } from './Fonts';

/**
 * Global styles for the WispAI app
 */
export const globalStyles = StyleSheet.create({
  // Apply Sato<PERSON> font to all text
  text: {
    fontFamily: 'Satoshi-Regular',
  },

  // Common text styles
  heading1: {
    fontFamily: FontFamily.black,
    fontSize: 32,
    lineHeight: 40,
  },

  heading2: {
    fontFamily: FontFamily.bold,
    fontSize: 24,
    lineHeight: 32,
  },

  heading3: {
    fontFamily: FontFamily.bold,
    fontSize: 20,
    lineHeight: 28,
  },

  body: {
    fontFamily: FontFamily.regular,
    fontSize: 16,
    lineHeight: 24,
  },

  caption: {
    fontFamily: FontFamily.light,
    fontSize: 14,
    lineHeight: 20,
  },

  // Common layout styles
  container: {
    flex: 1,
    backgroundColor: '#0A0A0A',
  },

  // Common spacing
  padding: {
    padding: 16,
  },

  paddingHorizontal: {
    paddingHorizontal: 16,
  },

  paddingVertical: {
    paddingVertical: 16,
  },

  margin: {
    margin: 16,
  },

  marginHorizontal: {
    marginHorizontal: 16,
  },

  marginVertical: {
    marginVertical: 16,
  },
});

/**
 * Default text style to apply to all text in the app
 */
export const defaultTextStyle: TextStyle = {
  fontFamily: 'Satoshi-Regular',
  fontSize: 16,
  color: '#FFFFFF',
};

/**
 * Helper function to override default text styles in React Native components
 *
 * Note: This approach doesn't work well in Expo, so we'll use explicit styling instead
 */
export function applyFontToAll() {
  // This is a no-op function now
  console.log('Font styling will be applied through explicit component styling');
}
