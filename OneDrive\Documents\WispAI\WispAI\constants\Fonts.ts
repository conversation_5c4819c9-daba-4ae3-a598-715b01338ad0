/**
 * Font configuration for the WispAI app
 */

export const FontFamily = {
  // Satoshi font family
  regular: 'Satoshi-Regular',
  medium: 'Satoshi-Medium',
  bold: 'Satoshi-Bold',
  black: 'Satoshi-Black',
  light: 'Satoshi-Light',
  italic: 'Satoshi-Italic',
  mediumItalic: 'Satoshi-MediumItalic',
  boldItalic: 'Satoshi-BoldItalic',
  blackItalic: 'Satoshi-BlackItalic',
  lightItalic: 'Satoshi-LightItalic',
};

export const FontSize = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  display: 40,
};

export const FontWeight = {
  light: '300',
  regular: '400',
  medium: '500',
  bold: '700',
  black: '900',
};

export const LineHeight = {
  tight: 1.2,
  normal: 1.5,
  relaxed: 1.75,
  loose: 2,
};

export const LetterSpacing = {
  tighter: -0.8,
  tight: -0.4,
  normal: 0,
  wide: 0.4,
  wider: 0.8,
};

// Font styles for different text elements
export const Typography = {
  // Headings
  h1: {
    fontFamily: FontFamily.black,
    fontSize: FontSize.xxxl,
    lineHeight: LineHeight.tight,
    letterSpacing: LetterSpacing.tight,
  },
  h2: {
    fontFamily: FontFamily.bold,
    fontSize: FontSize.xxl,
    lineHeight: LineHeight.tight,
    letterSpacing: LetterSpacing.tight,
  },
  h3: {
    fontFamily: FontFamily.bold,
    fontSize: FontSize.xl,
    lineHeight: LineHeight.tight,
    letterSpacing: LetterSpacing.normal,
  },
  h4: {
    fontFamily: FontFamily.medium,
    fontSize: FontSize.lg,
    lineHeight: LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },

  // Body text
  body1: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.md,
    lineHeight: LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  body2: {
    fontFamily: FontFamily.regular,
    fontSize: FontSize.sm,
    lineHeight: LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },

  // Special text styles
  caption: {
    fontFamily: FontFamily.light,
    fontSize: FontSize.xs,
    lineHeight: LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
  button: {
    fontFamily: FontFamily.medium,
    fontSize: FontSize.md,
    lineHeight: LineHeight.tight,
    letterSpacing: LetterSpacing.wide,
  },
  label: {
    fontFamily: FontFamily.medium,
    fontSize: FontSize.sm,
    lineHeight: LineHeight.tight,
    letterSpacing: LetterSpacing.normal,
  },
  subtitle: {
    fontFamily: FontFamily.mediumItalic,
    fontSize: FontSize.md,
    lineHeight: LineHeight.normal,
    letterSpacing: LetterSpacing.normal,
  },
};

// Font loading configuration for Expo
export const fontConfig = {
  Satoshi: {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-Regular': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  // For now, we'll use the Regular font for all variants until you add more font files
  'Satoshi-Medium': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-Bold': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-Black': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-Light': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-Italic': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-MediumItalic': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-BoldItalic': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-BlackItalic': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
  'Satoshi-LightItalic': {
    uri: require('../assets/fonts/Satoshi-Regular.otf'),
    display: 'swap',
  },
};
