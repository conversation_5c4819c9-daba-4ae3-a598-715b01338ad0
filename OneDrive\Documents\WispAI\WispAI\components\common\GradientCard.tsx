import React from 'react';
import { StyleSheet, View, ViewStyle } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '../../contexts/ThemeContext';

interface GradientCardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  colors?: string[];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  borderColor?: string;
  borderWidth?: number;
  borderRadius?: number;
}

/**
 * A component that provides a gradient background for cards using the theme colors
 */
export default function GradientCard({
  children,
  style,
  colors,
  start = { x: 0, y: 0 },
  end = { x: 1, y: 1 },
  borderColor,
  borderWidth = 1,
  borderRadius = 8,
}: GradientCardProps) {
  const { theme } = useTheme();

  // Default to the theme's card gradient colors
  const gradientColors = colors || [
    theme.colors.cardGradient.start,
    theme.colors.cardGradient.end,
  ];

  // Default border color
  const cardBorderColor = borderColor || theme.colors.border;

  return (
    <LinearGradient
      colors={gradientColors}
      style={[
        styles.container,
        {
          borderColor: cardBorderColor,
          borderWidth,
          borderRadius,
        },
        style,
      ]}
      start={start}
      end={end}
    >
      {children}
    </LinearGradient>
  );
}

const styles = StyleSheet.create({
  container: {
    overflow: 'hidden',
  },
});
