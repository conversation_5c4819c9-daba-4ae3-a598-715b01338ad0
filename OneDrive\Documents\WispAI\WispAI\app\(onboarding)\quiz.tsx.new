import React, { useState, useRef } from 'react';
import {
  View,
  // Remove standard Text import
  // Text,
  StyleSheet,
  Dimensions,
  Animated,
  Easing,
  TouchableOpacity,
  StatusBar,
  ScrollView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { useTheme } from '../../contexts/ThemeContext';
import SwipeableCard from '../../components/common/SwipeableCard';
import GlassmorphicCard from '../../components/common/GlassmorphicCard';
import NeonButton from '../../components/common/NeonButton';
import { MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
// Import the custom Text component
import Text from '../../components/common/Text';
// Remove the old gradient import
// import { getQuestionGradient } from '../../constants/OnboardingGradients';
// Import the new gradient functions
import { getRandomGradient, GradientType } from '../../constants/Gradients';

const { width, height } = Dimensions.get('window');

// Dummy questions for the onboarding quiz
const questions = [
  {
    id: 1,
    question: "What brings you to WispAI?",
    options: [
      "I want to understand if someone is ghosting me",
      "I want to analyze conversation patterns",
      "I want to improve my communication skills",
      "Just curious about AI text analysis"
    ]
  },
  {
    id: 2,
    question: "Which messaging platform do you use most?",
    options: [
      "WhatsApp",
      "Instagram",
      "iMessage",
      "Facebook Messenger",
      "Telegram",
      "Other"
    ]
  },
  {
    id: 3,
    question: "What type of relationships do you want to analyze?",
    options: [
      "Dating/Romantic",
      "Friendships",
      "Professional",
      "Family",
      "All of the above"
    ]
  },
  {
    id: 4,
    question: "How often do you worry about being ghosted?",
    options: [
      "All the time",
      "Sometimes",
      "Rarely",
      "Never"
    ]
  }
];

export default function OnboardingQuiz() {
  const router = useRouter();
  const { theme } = useTheme();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<Record<number, string>>({});
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  // State to hold the current gradient
  const [currentGradient, setCurrentGradient] = useState<GradientType>(getRandomGradient());

  // Animation references
  const progressAnim = useRef(new Animated.Value(0)).current;
  const cardOpacityAnim = useRef(new Animated.Value(1)).current;
  const cardScaleAnim = useRef(new Animated.Value(1)).current;

  // Handle option selection
  const handleSelectOption = (option: string) => {
    setSelectedOption(option);
  };

  // Handle continuing to next question
  const handleNextQuestion = () => {
    if (selectedOption) {
      // Save the answer
      setAnswers({
        ...answers,
        [questions[currentQuestionIndex].id]: selectedOption
      });

      // Animate the card transition
      Animated.sequence([
        Animated.parallel([
          Animated.timing(cardOpacityAnim, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
          }),
          Animated.timing(cardScaleAnim, {
            toValue: 0.9,
            duration: 200,
            useNativeDriver: true,
            easing: Easing.bezier(0.4, 0, 0.2, 1),
          }),
        ]),
        Animated.timing(progressAnim, {
          toValue: (currentQuestionIndex + 1) / questions.length,
          duration: 300,
          useNativeDriver: false,
          easing: Easing.bezier(0.4, 0, 0.2, 1),
        }),
      ]).start(() => {
        // Move to next question or finish
        if (currentQuestionIndex < questions.length - 1) {
          setCurrentQuestionIndex(currentQuestionIndex + 1);
          setSelectedOption(null);

          // Set a new random gradient for the next question
          setCurrentGradient(getRandomGradient());

          // Reset animations for next card
          cardOpacityAnim.setValue(0);
          cardScaleAnim.setValue(0.9);

          // Animate the new card in
          Animated.parallel([
            Animated.timing(cardOpacityAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
              easing: Easing.bezier(0.4, 0, 0.2, 1),
            }),
            Animated.timing(cardScaleAnim, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
              easing: Easing.bezier(0.4, 0, 0.2, 1),
            }),
          ]).start();
        } else {
          // Quiz is complete, navigate to subscription page
          router.push('/subscription');
        }
      });
    }
  };

  // Skip the quiz
  const handleSkip = () => {
    router.push('/subscription');
  };

  // Current question data
  const currentQuestion = questions[currentQuestionIndex];

  return (
    <View style={styles.container}>
      {/* Background gradient that changes with each question */}
      <LinearGradient
        key={`gradient-${currentGradient.id}`} // Use gradient ID in key
        colors={currentGradient.colors}
        start={currentGradient.start}
        end={currentGradient.end}
        locations={currentGradient.locations}
        style={styles.background}
      />
      <StatusBar barStyle="light-content" />

      {/* Header with progress bar */}
      <View style={styles.header}>
        <View style={styles.progressBarContainer}>
          {/* Use custom Text component */}
          <Text style={[styles.progressText, { color: theme.colors.textSecondary }]}>
            {currentQuestionIndex + 1}/{questions.length}
          </Text>
          <View
            style={[
              styles.progressBarBackground,
              { backgroundColor: theme.colors.surfaceVariant }
            ]}
          >
            <Animated.View
              style={[
                styles.progressBar,
                {
                  backgroundColor: theme.colors.primary,
                  width: progressAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: ['0%', '100%'],
                  }),
                }
              ]}
            />
          </View>
        </View>

        <TouchableOpacity onPress={handleSkip} style={styles.skipButton}>
          <Text style={[styles.skipText, { color: theme.colors.textSecondary }]}>
            Skip
          </Text>
        </TouchableOpacity>
      </View>

      {/* Question card */}
      <Animated.View
        style={[
          styles.cardContainer,
          {
            opacity: cardOpacityAnim,
            transform: [{ scale: cardScaleAnim }],
          }
        ]}
      >
        <SwipeableCard
          onSwipeLeft={handleSkip}
          onSwipeRight={selectedOption ? handleNextQuestion : undefined}
          leftActionText="Skip"
          rightActionText="Next"
          leftActionColor={theme.colors.textSecondary}
          rightActionColor={theme.colors.primary}
          disabled={!selectedOption && currentQuestionIndex < questions.length - 1}
        >
          <GlassmorphicCard
            title={currentQuestion.question}
            glowColor={theme.colors.primary}
            glowIntensity="medium"
            style={styles.questionCard}
          >
            {/* Make options scrollable */}
            <ScrollView 
              style={styles.optionsScrollView}
              contentContainerStyle={styles.optionsContentContainer}
              showsVerticalScrollIndicator={false}
            >
              {currentQuestion.options.map((option, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.optionButton,
                    {
                      backgroundColor: selectedOption === option
                        ? theme.colors.primaryContainer
                        : theme.colors.surfaceVariant,
                      borderColor: selectedOption === option
                        ? theme.colors.primary
                        : 'transparent',
                      borderWidth: selectedOption === option ? 2 : 0,
                    }
                  ]}
                  onPress={() => handleSelectOption(option)}
                >
                  <Text
                    style={[
                      styles.optionText,
                      {
                        color: selectedOption === option
                          ? theme.colors.primary
                          : theme.colors.textPrimary
                      }
                    ]}
                  >
                    {option}
                  </Text>

                  {selectedOption === option && (
                    <MaterialIcons
                      name="check-circle"
                      size={24}
                      color={theme.colors.primary}
                      style={styles.checkIcon}
                    />
                  )}
                </TouchableOpacity>
              ))}
              {/* Add some bottom padding to ensure all options are visible */}
              <View style={styles.optionsPadding} />
            </ScrollView>
          </GlassmorphicCard>
        </SwipeableCard>
      </Animated.View>

      {/* Bottom navigation buttons */}
      <View style={styles.bottomContainer}>
        <NeonButton
          title="Next"
          variant="gradient"
          color="primary"
          fullWidth
          onPress={handleNextQuestion}
          disabled={!selectedOption}
          neonEffect
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
    zIndex: -1, // Ensure it's behind other content
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 40,
    marginBottom: 20,
  },
  progressBarContainer: {
    flex: 1,
    marginRight: 20,
  },
  progressText: {
    marginBottom: 8,
    fontSize: 14,
  },
  progressBarBackground: {
    height: 6,
    borderRadius: 3,
    width: '100%',
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 3,
  },
  skipButton: {
    padding: 8,
  },
  skipText: {
    fontSize: 16,
    fontWeight: '600',
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
  },
  questionCard: {
    width: width - 40,
    minHeight: 300,
    maxHeight: height * 0.6, // Limit height to 60% of screen height
  },
  // New styles for scrollable options
  optionsScrollView: {
    marginTop: 16,
    maxHeight: height * 0.4, // Limit height to 40% of screen height
  },
  optionsContentContainer: {
    paddingBottom: 10, // Add padding at the bottom
  },
  optionsContainer: {
    marginTop: 16,
  },
  optionButton: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  optionText: {
    fontSize: 16,
    flex: 1,
  },
  checkIcon: {
    marginLeft: 12,
  },
  bottomContainer: {
    width: '100%',
    paddingVertical: 20,
  },
  // Add padding at the bottom of options
  optionsPadding: {
    height: 20,
  },
});
