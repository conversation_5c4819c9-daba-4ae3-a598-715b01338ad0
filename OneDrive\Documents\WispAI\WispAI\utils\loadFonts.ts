import * as Font from 'expo-font';
import { fontConfig } from '../constants/Fonts';

/**
 * Load custom fonts for the app
 */
export async function loadFonts() {
  try {
    console.log('Starting to load fonts...');
    console.log('Font config:', JSON.stringify(fontConfig));
    await Font.loadAsync(fontConfig);
    console.log('Fonts loaded successfully');
    return true;
  } catch (error) {
    console.error('Error loading fonts:', error);
    return false;
  }
}
