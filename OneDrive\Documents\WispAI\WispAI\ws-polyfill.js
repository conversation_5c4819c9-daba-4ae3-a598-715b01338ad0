// Complete polyfill for the ws package to prevent Node.js module imports
// This file replaces the entire ws package with React Native compatible implementations

// Main WebSocket class - use React Native's native WebSocket
class WebSocket {
  constructor(url, protocols) {
    console.warn('WebSocket from ws package is not available in React Native. Use the native WebSocket API instead.');
    // Return a minimal WebSocket-like object
    this.readyState = 3; // CLOSED
    this.url = url;
    this.protocol = '';
    this.extensions = '';
    this.bufferedAmount = 0;
    this.binaryType = 'blob';
    
    // Event handlers
    this.onopen = null;
    this.onclose = null;
    this.onmessage = null;
    this.onerror = null;
    
    // Methods
    this.send = function() {
      console.warn('WebSocket send not available in polyfill');
    };
    this.close = function() {
      console.warn('WebSocket close not available in polyfill');
    };
    this.addEventListener = function() {};
    this.removeEventListener = function() {};
    this.dispatchEvent = function() { return false; };
  }
}

// WebSocket Server class (not supported in React Native)
class WebSocketServer {
  constructor() {
    console.warn('WebSocket Server is not supported in React Native');
  }
  
  on() { return this; }
  close() {}
  handleUpgrade() {}
}

// Export the main WebSocket class as default
module.exports = WebSocket;

// Export named exports
module.exports.WebSocket = WebSocket;
module.exports.WebSocketServer = WebSocketServer;
module.exports.default = WebSocket;

// Export constants
module.exports.CONNECTING = 0;
module.exports.OPEN = 1;
module.exports.CLOSING = 2;
module.exports.CLOSED = 3;
