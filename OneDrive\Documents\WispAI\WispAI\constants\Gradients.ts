/**
 * Curated collection of dark gradients for the WispAI app
 * Inspired by the provided gradient examples, aiming for richer visuals.
 */

// Gradient types
export type GradientType = {
  id: number;
  name: string; // Added name for easier reference
  colors: string[];
  start?: { x: number; y: number };
  end?: { x: number; y: number };
  locations?: number[]; // Control color stop positions
};

// Selected Dark Gradients Collection
export const darkGradients: GradientType[] = [
  // --- Inspired by 01-30 ---
  {
    id: 1,
    name: 'Sunset Flare',
    colors: ['#10081C', '#FF8C00', '#FFD700', '#F0E68C'],
    start: { x: 0.1, y: 0.1 },
    end: { x: 0.9, y: 0.9 },
    locations: [0, 0.5, 0.7, 1],
  },
  {
    id: 8,
    name: 'Arctic Glow',
    colors: ['#051422', '#00BFFF', '#AFEEEE', '#FFFFFF'],
    start: { x: 0.8, y: 0.1 },
    end: { x: 0.2, y: 0.9 },
    locations: [0, 0.4, 0.7, 1],
  },
  {
    id: 15,
    name: 'Volcanic Heat',
    colors: ['#1B0D05', '#DC143C', '#FF8C00', '#FFD700'],
    start: { x: 0.5, y: 0 },
    end: { x: 0.5, y: 1 },
    locations: [0, 0.3, 0.6, 1],
  },
  {
    id: 21,
    name: 'Emerald Depths',
    colors: ['#02100C', '#00FA9A', '#90EE90', '#FFFFE0'],
    start: { x: 0.2, y: 0.8 },
    end: { x: 0.8, y: 0.2 },
    locations: [0, 0.4, 0.7, 1],
  },
  {
    id: 25,
    name: 'Cosmic Fuchsia',
    colors: ['#190719', '#FF00FF', '#DA70D6', '#FFB6C1'],
    start: { x: 0, y: 0.5 },
    end: { x: 1, y: 0.5 },
    locations: [0, 0.4, 0.7, 1],
  },
  // --- Inspired by 31-60 ---
  {
    id: 31,
    name: 'Deep Ocean',
    colors: ['#000010', '#0A0A2A', '#0077CC', '#00E5FF'],
    start: { x: 0, y: 0 },
    end: { x: 1, y: 1 },
    locations: [0, 0.3, 0.6, 1],
  },
  {
    id: 49,
    name: 'Magenta Pulse',
    colors: ['#150015', '#FF00FF', '#8A2BE2', '#4B0082'],
    start: { x: 0.1, y: 0.9 },
    end: { x: 0.9, y: 0.1 },
    locations: [0, 0.4, 0.7, 1],
  },
  // --- Inspired by 61-90 ---
  {
    id: 61,
    name: 'Pink Horizon',
    colors: ['#1A0A10', '#FF69B4', '#FFB6C1', '#FFE4E1'],
    start: { x: 0.7, y: 0 },
    end: { x: 0.3, y: 1 },
    locations: [0, 0.4, 0.7, 1],
  },
  {
    id: 68,
    name: 'Aqua Haze',
    colors: ['#051210', '#40E0D0', '#AFEEEE', '#E0FFFF'],
    start: { x: 0.3, y: 0.1 },
    end: { x: 0.7, y: 0.9 },
    locations: [0, 0.4, 0.7, 1],
  },
  {
    id: 74,
    name: 'Fiery Teal',
    colors: ['#1C0D05', '#FF4500', '#FFD700', '#008080'],
    start: { x: 0.9, y: 0.2 },
    end: { x: 0.1, y: 0.8 },
    locations: [0, 0.3, 0.6, 1],
  },
  // --- Inspired by 91-120 ---
  {
    id: 91,
    name: 'Neon Berry',
    colors: ['#100515', '#FF1493', '#FF00FF', '#FF69B4'],
    start: { x: 0, y: 0.2 },
    end: { x: 1, y: 0.8 },
    locations: [0, 0.4, 0.7, 1],
  },
  {
    id: 96,
    name: 'Lime Twist',
    colors: ['#0A1005', '#32CD32', '#ADFF2F', '#FFFFE0'],
    start: { x: 0.8, y: 0.8 },
    end: { x: 0.2, y: 0.2 },
    locations: [0, 0.4, 0.7, 1],
  },
  // --- Inspired by 121-150 ---
  {
    id: 121,
    name: 'Golden Hour',
    colors: ['#1C1005', '#FF8C00', '#FFA500', '#FFD700'],
    start: { x: 0.1, y: 0.1 },
    end: { x: 0.9, y: 0.9 },
    locations: [0, 0.4, 0.7, 1],
  },
  {
    id: 135,
    name: 'Amethyst Mist',
    colors: ['#10051C', '#8A2BE2', '#9370DB', '#E6E6FA'],
    start: { x: 0.5, y: 0.9 },
    end: { x: 0.5, y: 0.1 },
    locations: [0, 0.4, 0.7, 1],
  },
  {
    id: 150,
    name: 'Minty Wave',
    colors: ['#051C10', '#3EB489', '#98FB98', '#F0FFF0'],
    start: { x: 0.9, y: 0.9 },
    end: { x: 0.1, y: 0.1 },
    locations: [0, 0.4, 0.7, 1],
  },
];

// Helper function to get a gradient by ID
export const getGradientById = (id: number): GradientType | undefined => {
  return darkGradients.find(gradient => gradient.id === id);
};

// Helper function to get a random gradient
export const getRandomGradient = (): GradientType => {
  const randomIndex = Math.floor(Math.random() * darkGradients.length);
  return darkGradients[randomIndex];
};
